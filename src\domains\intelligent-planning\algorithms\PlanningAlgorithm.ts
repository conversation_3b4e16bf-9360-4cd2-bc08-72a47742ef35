/**
 * 时间规划算法
 * 负责根据任务优先级和用户时间配置生成智能的时间安排
 */

import {
  Task,
  ScoredTask,
  DailySchedule,
  TimeSlot,
  UserTimeConfig,
  Priority,
  Quadrant,
  TaskCategory,
  DEFAULT_USER_CONFIG,
  ALGORITHM_CONSTANTS,
  QUADRANTS,
  classifyQuadrant,
  getTodayTasks,
  isTimeRangeOverlap
} from '@/shared';

export class PlanningAlgorithm {
  
  /**
   * 生成今日时间安排
   */
  generateDailySchedule(tasks: Task[], userTimeConfig?: UserTimeConfig): DailySchedule {
    // 1. 过滤今日需要处理的任务
    const todayTasks = this.filterTodayTasks(tasks);

    // 2. 计算分数并分类
    const scoredTasks: ScoredTask[] = todayTasks
      .map(task => ({
        ...task,
        score: this.calculateTaskScore(task),
        quadrant: classifyQuadrant(task.importance, task.urgency)
      }))
      .sort((a, b) => {
        // 先按象限排序，再按分数排序
        if (a.quadrant !== b.quadrant) {
          return a.quadrant - b.quadrant;
        }
        return b.score - a.score;
      });

    // 3. 生成时间段（使用新的基于任务类型的算法）
    const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);

    // 4. 计算总时长
    const totalDuration = timeSlots.reduce((sum, slot) =>
      sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0
    );

    return {
      date: new Date(),
      timeSlots,
      totalTasks: todayTasks.length,
      estimatedDuration: totalDuration
    };
  }

  /**
   * 过滤今日需要处理的任务
   */
  private filterTodayTasks(tasks: Task[]): Task[] {
    return getTodayTasks(tasks);
  }

  /**
   * 计算任务综合分数
   */
  private calculateTaskScore(task: Task): number {
    const quadrant = classifyQuadrant(task.importance, task.urgency);
    
    // 基础分数：象限权重 * (重要性 + 紧急性)
    const baseScore = ALGORITHM_CONSTANTS.QUADRANT_WEIGHTS[quadrant] * 
                     (task.importance + task.urgency);
    
    // 截止日期权重
    const now = new Date();
    const timeToDeadline = task.deadline.getTime() - now.getTime();
    const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);
    
    let deadlineWeight = 1;
    if (hoursToDeadline < 2) {
      deadlineWeight = 2.0; // 2小时内，权重翻倍
    } else if (hoursToDeadline < 24) {
      deadlineWeight = 1.5; // 24小时内，权重增加50%
    } else if (hoursToDeadline < 48) {
      deadlineWeight = 1.2; // 48小时内，权重增加20%
    }
    
    // 推迟惩罚
    const postponePenalty = task.postponeCount * ALGORITHM_CONSTANTS.POSTPONE_PENALTY;
    
    // 分类权重
    const categoryWeight = ALGORITHM_CONSTANTS.CATEGORY_WEIGHTS[task.category];
    
    return baseScore * deadlineWeight * categoryWeight * (1 + postponePenalty);
  }

  /**
   * 基于任务类型生成时间段安排
   */
  private generateTimeSlotsWithCategories(tasks: ScoredTask[], userTimeConfig?: UserTimeConfig): TimeSlot[] {
    const timeSlots: TimeSlot[] = [];
    const today = new Date();

    // 使用默认配置如果没有提供用户配置
    const config = userTimeConfig || DEFAULT_USER_CONFIG;

    // 按任务类型分组
    const tasksByCategory = this.groupTasksByCategory(tasks);

    // 为每种类型的任务安排时间
    for (const [category, categoryTasks] of Object.entries(tasksByCategory)) {
      if (categoryTasks.length === 0) continue;

      const categoryPrefs = config.categoryPreferences[category as TaskCategory];
      if (!categoryPrefs) continue;

      // 为该类型任务生成可用时间段
      const availableSlots = this.generateAvailableSlots(
        categoryPrefs.preferredTimes, 
        config.fixedSlots, 
        today
      );

      // 在可用时间段中安排任务
      this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);
    }

    return timeSlots.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  /**
   * 按任务类型分组
   */
  private groupTasksByCategory(tasks: ScoredTask[]): Record<TaskCategory, ScoredTask[]> {
    return {
      work: tasks.filter(task => task.category === 'work'),
      improvement: tasks.filter(task => task.category === 'improvement'),
      entertainment: tasks.filter(task => task.category === 'entertainment')
    };
  }

  /**
   * 生成可用时间段
   */
  private generateAvailableSlots(
    preferredTimes: string[], 
    fixedSlots: any[], 
    date: Date
  ): { start: Date; end: Date }[] {
    const availableSlots: { start: Date; end: Date }[] = [];

    for (const timeRange of preferredTimes) {
      const [startTime, endTime] = timeRange.split('-');
      const [startHour, startMinute] = startTime.split(':').map(Number);
      const [endHour, endMinute] = endTime.split(':').map(Number);

      const slotStart = new Date(date);
      slotStart.setHours(startHour, startMinute, 0, 0);

      const slotEnd = new Date(date);
      slotEnd.setHours(endHour, endMinute, 0, 0);

      // 检查是否与固定时间段冲突
      let hasConflict = false;
      for (const fixedSlot of fixedSlots) {
        const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);
        const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);

        const fixedStart = new Date(date);
        fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);

        const fixedEnd = new Date(date);
        fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);

        // 检查时间段重叠
        if (isTimeRangeOverlap(slotStart, slotEnd, fixedStart, fixedEnd)) {
          hasConflict = true;
          break;
        }
      }

      if (!hasConflict) {
        availableSlots.push({ start: slotStart, end: slotEnd });
      }
    }

    return availableSlots;
  }

  /**
   * 在可用时间段中安排任务
   */
  private scheduleTasksInSlots(
    tasks: ScoredTask[], 
    availableSlots: { start: Date; end: Date }[], 
    timeSlots: TimeSlot[]
  ): void {
    let currentSlotIndex = 0;
    let currentTime = availableSlots[0]?.start;

    if (!currentTime) return;

    for (const task of tasks) {
      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒

      // 寻找合适的时间段
      while (currentSlotIndex < availableSlots.length) {
        const currentSlot = availableSlots[currentSlotIndex];
        const remainingTime = currentSlot.end.getTime() - currentTime.getTime();

        if (remainingTime >= taskDuration) {
          // 在当前时间段安排任务
          const endTime = new Date(currentTime.getTime() + taskDuration);

          timeSlots.push({
            task,
            startTime: new Date(currentTime),
            endTime,
            isFixed: false
          });

          // 更新当前时间，添加15分钟休息时间
          currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
          break;
        } else {
          // 移动到下一个时间段
          currentSlotIndex++;
          currentTime = availableSlots[currentSlotIndex]?.start;
          if (!currentTime) break;
        }
      }
    }
  }

  /**
   * 获取任务建议
   */
  getTaskRecommendation(task: Task): string {
    const quadrant = classifyQuadrant(task.importance, task.urgency);

    if (quadrant === QUADRANTS.URGENT_IMPORTANT) {
      return '🔥 高优先级任务，建议立即处理';
    } else if (quadrant === QUADRANTS.IMPORTANT_NOT_URGENT) {
      return '📅 重要任务，建议合理安排时间';
    } else if (quadrant === QUADRANTS.URGENT_NOT_IMPORTANT) {
      return '⚡ 紧急但不重要，考虑委托或快速处理';
    } else {
      return '🤔 优先级较低，可以延后或删除';
    }
  }

  /**
   * 生成时间段安排（保留原方法作为后备）
   */
  generateTimeSlots(tasks: ScoredTask[], workHours: { start: string; end: string }): TimeSlot[] {
    const timeSlots: TimeSlot[] = [];
    const today = new Date();

    // 解析工作时间
    const [startHour, startMinute] = workHours.start.split(':').map(Number);
    const [endHour, endMinute] = workHours.end.split(':').map(Number);

    let currentTime = new Date(today);
    currentTime.setHours(startHour, startMinute, 0, 0);

    const workEndTime = new Date(today);
    workEndTime.setHours(endHour, endMinute, 0, 0);

    for (const task of tasks) {
      // 检查是否还有足够的工作时间
      const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();
      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒

      if (remainingWorkTime < taskDuration) {
        // 如果当天时间不够，跳过或安排到明天
        continue;
      }

      const endTime = new Date(currentTime.getTime() + taskDuration);

      timeSlots.push({
        task,
        startTime: new Date(currentTime),
        endTime,
        isFixed: false
      });

      // 更新当前时间，添加15分钟休息时间
      currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);

      // 如果超过工作时间，停止安排
      if (currentTime >= workEndTime) {
        break;
      }
    }

    return timeSlots;
  }
}
