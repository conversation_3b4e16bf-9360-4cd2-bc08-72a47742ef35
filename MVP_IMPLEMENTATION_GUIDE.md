# TimeManager MVP 实现指南

## 🚀 快速启动

### 1. 项目初始化
```bash
# 创建Next.js项目
npx create-next-app@latest timemanager-mvp --typescript --tailwind --app
cd timemanager-mvp

# 安装核心依赖
npm install @supabase/supabase-js zustand @tanstack/react-query
npm install date-fns lucide-react @radix-ui/react-dialog
npm install @tauri-apps/api @tauri-apps/cli

# 安装开发依赖
npm install -D @types/node prettier eslint-config-prettier
```

### 2. 项目结构
```
timemanager-mvp/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── dashboard/       # 主控制台
│   │   ├── tasks/          # 任务管理
│   │   ├── planning/       # 今日规划
│   │   └── settings/       # 设置页面
│   ├── components/         # 可复用组件
│   │   ├── ui/            # 基础UI组件
│   │   ├── tasks/         # 任务相关组件
│   │   └── planning/      # 规划相关组件
│   ├── lib/               # 工具库
│   │   ├── supabase.ts    # 数据库配置
│   │   ├── algorithms/    # 三大算法
│   │   └── calendar.ts    # 日历集成
│   ├── hooks/             # 自定义Hooks
│   ├── store/             # 状态管理
│   └── types/             # TypeScript类型
├── src-tauri/             # Tauri桌面应用
│   ├── src/
│   │   └── main.rs
│   └── tauri.conf.json
└── public/
```

## 🔧 核心算法实现

### 1. 时间规划算法
```typescript
// src/lib/algorithms/planning.ts
export class PlanningAlgorithm {
  calculateTaskScore(task: Task): number {
    const baseScore = task.importance * 0.6 + task.urgency * 0.4;
    const categoryBonus = {
      work: 0,
      improvement: 2,
      entertainment: 1
    }[task.category];
    const postponePenalty = task.postponeCount * 3;
    
    return baseScore + categoryBonus + postponePenalty;
  }
  
  classifyQuadrant(importance: number, urgency: number): 1 | 2 | 3 | 4 {
    if (importance >= 4 && urgency >= 4) return 1; // 重要紧急
    if (importance >= 4 && urgency < 4) return 2;  // 重要不紧急
    if (importance < 4 && urgency >= 4) return 3;  // 不重要紧急
    return 4; // 不重要不紧急
  }
  
  generateDailySchedule(tasks: Task[]): DailySchedule {
    // 1. 计算分数并排序
    const scoredTasks = tasks
      .map(task => ({
        ...task,
        score: this.calculateTaskScore(task),
        quadrant: this.classifyQuadrant(task.importance, task.urgency)
      }))
      .sort((a, b) => b.score - a.score);
    
    // 2. 生成时间段
    const timeSlots: TimeSlot[] = [];
    let currentTime = new Date();
    currentTime.setHours(9, 0, 0, 0); // 从早上9点开始
    
    scoredTasks.forEach(task => {
      const duration = task.estimatedDuration || 60; // 默认60分钟
      const endTime = new Date(currentTime.getTime() + duration * 60000);
      
      timeSlots.push({
        task,
        startTime: new Date(currentTime),
        endTime,
        isFixed: false
      });
      
      currentTime = new Date(endTime.getTime() + 15 * 60000); // 15分钟休息
    });
    
    return {
      date: new Date(),
      timeSlots,
      totalTasks: tasks.length,
      estimatedDuration: timeSlots.reduce((sum, slot) => 
        sum + (slot.endTime.getTime() - slot.startTime.getTime()) / 60000, 0
      )
    };
  }
}
```

### 2. 生活平衡算法
```typescript
// src/lib/algorithms/balance.ts
export class BalanceAlgorithm {
  async analyzeWeeklyBalance(userId: string): Promise<BalanceAnalysis> {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    // 获取最近7天的统计数据
    const stats = await this.getDailyStats(userId, sevenDaysAgo);
    
    const totalTime = stats.reduce((sum, day) => 
      sum + day.workTime + day.improvementTime + day.entertainmentTime, 0
    );
    
    if (totalTime === 0) {
      return {
        workRatio: 0,
        improvementRatio: 0,
        entertainmentRatio: 0,
        balanceScore: 0,
        recommendation: "开始记录你的任务来获得平衡分析"
      };
    }
    
    const ratios = {
      work: stats.reduce((sum, day) => sum + day.workTime, 0) / totalTime,
      improvement: stats.reduce((sum, day) => sum + day.improvementTime, 0) / totalTime,
      entertainment: stats.reduce((sum, day) => sum + day.entertainmentTime, 0) / totalTime
    };
    
    const balanceScore = this.calculateBalanceScore(ratios);
    const recommendation = this.generateRecommendation(ratios);
    
    return {
      ...ratios,
      balanceScore,
      recommendation
    };
  }
  
  private calculateBalanceScore(ratios: CategoryRatios): number {
    const ideal = { work: 0.6, improvement: 0.25, entertainment: 0.15 };
    let score = 100;
    
    Object.keys(ideal).forEach(category => {
      const deviation = Math.abs(ideal[category] - ratios[category]);
      score -= deviation * 100;
    });
    
    return Math.max(0, Math.round(score));
  }
  
  private generateRecommendation(ratios: CategoryRatios): string {
    if (ratios.work > 0.8) {
      return "工作时间过多，建议增加休息和娱乐时间 🎮";
    } else if (ratios.improvement < 0.1) {
      return "建议安排一些学习或自我提升的活动 📚";
    } else if (ratios.entertainment < 0.05) {
      return "需要更多的放松和娱乐时间 🎵";
    } else if (ratios.work < 0.4) {
      return "可以适当增加工作或学习时间 💼";
    } else {
      return "生活平衡状态良好，继续保持！ ✨";
    }
  }
}
```

### 3. 修复算法
```typescript
// src/lib/algorithms/fix.ts
export class FixAlgorithm {
  async analyzePostponedTasks(userId: string): Promise<PostponedTaskAlert[]> {
    const tasks = await this.getPostponedTasks(userId);
    
    return tasks.map(task => {
      const urgencyLevel = this.calculateUrgencyLevel(task);
      const suggestion = this.generateFixSuggestion(task);
      
      return {
        task,
        postponeCount: task.postponeCount,
        daysSinceCreated: this.calculateDaysSince(task.createdAt),
        urgencyLevel,
        suggestion,
        shouldAlert: urgencyLevel !== 'low'
      };
    });
  }
  
  private calculateUrgencyLevel(task: Task): 'low' | 'medium' | 'high' | 'critical' {
    const { postponeCount } = task;
    const daysSinceDeadline = this.calculateDaysSince(task.deadline);
    
    if (postponeCount >= 5 || daysSinceDeadline > 3) return 'critical';
    if (postponeCount >= 3 || daysSinceDeadline > 1) return 'high';
    if (postponeCount >= 2 || daysSinceDeadline > 0) return 'medium';
    return 'low';
  }
  
  private generateFixSuggestion(task: Task): string {
    const suggestions = {
      critical: "🚨 紧急：建议立即处理或重新评估任务的必要性",
      high: "⚠️ 重要：建议分解为更小的子任务，或调整截止时间",
      medium: "💡 提醒：建议设置更具体的时间安排",
      low: "📝 建议：可以适当调整任务优先级"
    };
    
    const urgency = this.calculateUrgencyLevel(task);
    return suggestions[urgency];
  }
}
```

## 📱 推送机制实现

### 1. 日历集成
```typescript
// src/lib/calendar.ts
export class CalendarIntegration {
  async syncToGoogleCalendar(schedule: DailySchedule): Promise<void> {
    const events = schedule.timeSlots.map(slot => ({
      summary: `📋 ${slot.task.title}`,
      description: this.formatTaskDescription(slot.task),
      start: {
        dateTime: slot.startTime.toISOString(),
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
      },
      end: {
        dateTime: slot.endTime.toISOString(),
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
      },
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'popup', minutes: 15 },
          { method: 'popup', minutes: 5 }
        ]
      },
      colorId: this.getCategoryColor(slot.task.category)
    }));
    
    // 调用Google Calendar API
    await this.createBatchEvents(events);
  }
  
  private formatTaskDescription(task: Task): string {
    return `
分类: ${task.category}
重要性: ${task.importance}/5
紧急性: ${task.urgency}/5
预估时长: ${task.estimatedDuration}分钟
    `.trim();
  }
  
  private getCategoryColor(category: string): string {
    const colors = {
      work: '1',        // 蓝色
      improvement: '5', // 黄色
      entertainment: '10' // 绿色
    };
    return colors[category] || '1';
  }
}
```

### 2. 桌面悬浮窗（Tauri）
```rust
// src-tauri/src/main.rs
use tauri::{CustomMenuItem, SystemTray, SystemTrayMenu, SystemTrayEvent};
use tauri::{Manager, Window};

fn main() {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let show = CustomMenuItem::new("show".to_string(), "显示");
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(quit);
    
    let system_tray = SystemTray::new().with_menu(tray_menu);
    
    tauri::Builder::default()
        .system_tray(system_tray)
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick { .. } => {
                let window = app.get_window("main").unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            SystemTrayEvent::MenuItemClick { id, .. } => {
                match id.as_str() {
                    "quit" => {
                        std::process::exit(0);
                    }
                    "show" => {
                        let window = app.get_window("main").unwrap();
                        window.show().unwrap();
                    }
                    _ => {}
                }
            }
            _ => {}
        })
        .invoke_handler(tauri::generate_handler![
            show_notification,
            get_current_task,
            mark_task_complete
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
async fn show_notification(title: String, body: String) -> Result<(), String> {
    tauri::api::notification::Notification::new("timemanager")
        .title(&title)
        .body(&body)
        .show()
        .map_err(|e| e.to_string())?;
    Ok(())
}
```

### 3. 桌面悬浮窗UI
```typescript
// src/components/desktop/FloatingWidget.tsx
export function FloatingWidget() {
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [todayProgress, setTodayProgress] = useState({ completed: 0, total: 0 });
  
  useEffect(() => {
    // 监听来自主应用的数据更新
    const unlisten = listen('task-update', (event) => {
      setCurrentTask(event.payload.currentTask);
      setTodayProgress(event.payload.progress);
    });
    
    return () => {
      unlisten.then(f => f());
    };
  }, []);
  
  const handleCompleteTask = async () => {
    if (currentTask) {
      await invoke('mark_task_complete', { taskId: currentTask.id });
      await invoke('show_notification', {
        title: '任务完成',
        body: `"${currentTask.title}" 已完成！`
      });
    }
  };
  
  return (
    <div className="w-80 h-96 bg-white rounded-lg shadow-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold">今日任务</h3>
        <span className="text-sm text-gray-500">
          {todayProgress.completed}/{todayProgress.total}
        </span>
      </div>
      
      {currentTask ? (
        <div className="space-y-3">
          <div className="p-3 bg-blue-50 rounded-lg">
            <h4 className="font-medium">{currentTask.title}</h4>
            <p className="text-sm text-gray-600">
              {currentTask.category} • {currentTask.estimatedDuration}分钟
            </p>
          </div>
          
          <button
            onClick={handleCompleteTask}
            className="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600"
          >
            完成任务
          </button>
        </div>
      ) : (
        <div className="text-center text-gray-500">
          暂无进行中的任务
        </div>
      )}
    </div>
  );
}
```

## 🎯 MVP部署配置

### 1. Supabase配置
```sql
-- 在Supabase SQL编辑器中执行
-- 启用实时功能
ALTER PUBLICATION supabase_realtime ADD TABLE tasks;
ALTER PUBLICATION supabase_realtime ADD TABLE daily_stats;

-- 创建触发器自动更新统计
CREATE OR REPLACE FUNCTION update_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO daily_stats (user_id, date, tasks_completed)
  VALUES (NEW.user_id, CURRENT_DATE, 1)
  ON CONFLICT (user_id, date)
  DO UPDATE SET tasks_completed = daily_stats.tasks_completed + 1;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER task_completion_trigger
  AFTER UPDATE OF status ON tasks
  FOR EACH ROW
  WHEN (NEW.status = 'completed' AND OLD.status != 'completed')
  EXECUTE FUNCTION update_daily_stats();
```

### 2. 环境变量配置
```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_GOOGLE_CALENDAR_API_KEY=your_google_api_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. Vercel部署
```json
// vercel.json
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 10
    }
  },
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase_url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key"
  }
}
```

这个MVP实现指南提供了完整的技术实现路径，你觉得如何？我们可以开始第一步的项目初始化吗？
