/**
 * 引导服务
 * 负责用户引导流程的管理
 */

import {
  UserTimeConfig,
  ApiResponse,
  DEFAULT_USER_CONFIG
} from '@/shared';

import {
  OnboardingStep,
  OnboardingState,
  TimePreferences,
  validateTimeConfig,
  getDefaultTimeConfig
} from '../models/UserProfile';

export class OnboardingService {

  /**
   * 获取引导步骤配置
   */
  getOnboardingSteps(): OnboardingStep[] {
    return [
      {
        id: 'welcome',
        title: '欢迎使用 TimeManager',
        description: '让我们花几分钟时间了解您的时间偏好，为您提供个性化的时间管理体验。',
        component: 'WelcomeStep',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'work-schedule',
        title: '工作时间设置',
        description: '设置您的工作时间和工作日，帮助我们更好地安排您的任务。',
        component: 'WorkScheduleStep',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'sleep-schedule',
        title: '作息时间设置',
        description: '设置您的睡眠时间，确保任务安排不会影响您的休息。',
        component: 'SleepScheduleStep',
        isCompleted: false,
        isRequired: true
      },
      {
        id: 'fixed-slots',
        title: '固定时间段',
        description: '设置您的固定时间段，如用餐时间、通勤时间等。',
        component: 'FixedSlotsStep',
        isCompleted: false,
        isRequired: false
      },
      {
        id: 'preferences',
        title: '个人偏好',
        description: '设置您的工作偏好和时间管理策略。',
        component: 'PreferencesStep',
        isCompleted: false,
        isRequired: false
      },
      {
        id: 'category-preferences',
        title: '任务分类偏好',
        description: '为不同类型的任务设置偏好时间段。',
        component: 'CategoryPreferencesStep',
        isCompleted: false,
        isRequired: false
      },
      {
        id: 'completion',
        title: '设置完成',
        description: '恭喜！您的个性化设置已完成，现在可以开始使用 TimeManager 了。',
        component: 'CompletionStep',
        isCompleted: false,
        isRequired: true
      }
    ];
  }

  /**
   * 初始化引导状态
   */
  initializeOnboardingState(): OnboardingState {
    const steps = this.getOnboardingSteps();
    
    return {
      currentStep: 0,
      totalSteps: steps.length,
      steps,
      isCompleted: false,
      timeConfig: getDefaultTimeConfig()
    };
  }

  /**
   * 验证当前步骤
   */
  validateCurrentStep(state: OnboardingState): ApiResponse<boolean> {
    const currentStep = state.steps[state.currentStep];
    
    if (!currentStep) {
      return {
        success: false,
        error: '无效的步骤',
        data: false
      };
    }

    switch (currentStep.id) {
      case 'welcome':
        return { success: true, data: true };
        
      case 'work-schedule':
        return this.validateWorkSchedule(state.timeConfig);
        
      case 'sleep-schedule':
        return this.validateSleepSchedule(state.timeConfig);
        
      case 'fixed-slots':
        return this.validateFixedSlots(state.timeConfig);
        
      case 'preferences':
        return this.validatePreferences(state.timeConfig);
        
      case 'category-preferences':
        return this.validateCategoryPreferences(state.timeConfig);
        
      case 'completion':
        return { success: true, data: true };
        
      default:
        return { success: true, data: true };
    }
  }

  /**
   * 验证工作时间设置
   */
  private validateWorkSchedule(config: Partial<UserTimeConfig>): ApiResponse<boolean> {
    const errors: string[] = [];

    if (!config.workStart) {
      errors.push('请设置工作开始时间');
    }

    if (!config.workEnd) {
      errors.push('请设置工作结束时间');
    }

    if (config.workStart && config.workEnd) {
      const startTime = new Date(`2000-01-01 ${config.workStart}`);
      const endTime = new Date(`2000-01-01 ${config.workEnd}`);
      
      if (startTime >= endTime) {
        errors.push('工作结束时间必须晚于开始时间');
      }

      const workDuration = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
      if (workDuration > 12) {
        errors.push('工作时间不应超过12小时');
      }
    }

    if (!config.workDays || config.workDays.length === 0) {
      errors.push('请至少选择一个工作日');
    }

    return {
      success: errors.length === 0,
      error: errors.join('; '),
      data: errors.length === 0
    };
  }

  /**
   * 验证睡眠时间设置
   */
  private validateSleepSchedule(config: Partial<UserTimeConfig>): ApiResponse<boolean> {
    const errors: string[] = [];

    if (!config.sleepStart) {
      errors.push('请设置睡眠开始时间');
    }

    if (!config.sleepEnd) {
      errors.push('请设置睡眠结束时间');
    }

    if (config.sleepStart && config.sleepEnd) {
      const sleepStart = new Date(`2000-01-01 ${config.sleepStart}`);
      const sleepEnd = new Date(`2000-01-02 ${config.sleepEnd}`); // 次日
      
      const sleepDuration = (sleepEnd.getTime() - sleepStart.getTime()) / (1000 * 60 * 60);
      if (sleepDuration < 6) {
        errors.push('睡眠时间不应少于6小时');
      } else if (sleepDuration > 12) {
        errors.push('睡眠时间不应超过12小时');
      }
    }

    return {
      success: errors.length === 0,
      error: errors.join('; '),
      data: errors.length === 0
    };
  }

  /**
   * 验证固定时间段设置
   */
  private validateFixedSlots(config: Partial<UserTimeConfig>): ApiResponse<boolean> {
    const errors: string[] = [];

    if (config.fixedSlots) {
      for (const slot of config.fixedSlots) {
        if (!slot.start || !slot.end) {
          errors.push('固定时间段必须设置开始和结束时间');
          continue;
        }

        const startTime = new Date(`2000-01-01 ${slot.start}`);
        const endTime = new Date(`2000-01-01 ${slot.end}`);
        
        if (startTime >= endTime) {
          errors.push(`${slot.label || '固定时间段'} 的结束时间必须晚于开始时间`);
        }
      }

      // 检查时间段重叠
      for (let i = 0; i < config.fixedSlots.length; i++) {
        for (let j = i + 1; j < config.fixedSlots.length; j++) {
          const slot1 = config.fixedSlots[i];
          const slot2 = config.fixedSlots[j];
          
          const start1 = new Date(`2000-01-01 ${slot1.start}`);
          const end1 = new Date(`2000-01-01 ${slot1.end}`);
          const start2 = new Date(`2000-01-01 ${slot2.start}`);
          const end2 = new Date(`2000-01-01 ${slot2.end}`);
          
          if (start1 < end2 && end1 > start2) {
            errors.push(`${slot1.label || '时间段'} 与 ${slot2.label || '时间段'} 存在时间冲突`);
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      error: errors.join('; '),
      data: errors.length === 0
    };
  }

  /**
   * 验证个人偏好设置
   */
  private validatePreferences(config: Partial<UserTimeConfig>): ApiResponse<boolean> {
    const errors: string[] = [];

    if (config.maxContinuousWork !== undefined) {
      if (config.maxContinuousWork < 30 || config.maxContinuousWork > 240) {
        errors.push('最大连续工作时间应在30-240分钟之间');
      }
    }

    if (config.breakInterval !== undefined) {
      if (config.breakInterval < 5 || config.breakInterval > 60) {
        errors.push('休息间隔应在5-60分钟之间');
      }
    }

    if (config.commuteToWork !== undefined) {
      if (config.commuteToWork < 0 || config.commuteToWork > 180) {
        errors.push('通勤时间应在0-180分钟之间');
      }
    }

    return {
      success: errors.length === 0,
      error: errors.join('; '),
      data: errors.length === 0
    };
  }

  /**
   * 验证任务分类偏好设置
   */
  private validateCategoryPreferences(config: Partial<UserTimeConfig>): ApiResponse<boolean> {
    const errors: string[] = [];

    if (config.categoryPreferences) {
      const categories = ['work', 'improvement', 'entertainment'] as const;
      
      for (const category of categories) {
        const pref = config.categoryPreferences[category];
        if (pref) {
          if (pref.maxDaily < 0 || pref.maxDaily > 720) {
            errors.push(`${category} 类型任务的每日最大时间应在0-720分钟之间`);
          }

          if (pref.preferredTimes) {
            for (const timeRange of pref.preferredTimes) {
              const [start, end] = timeRange.split('-');
              if (!start || !end) {
                errors.push(`${category} 类型任务的偏好时间格式不正确`);
                continue;
              }

              const startTime = new Date(`2000-01-01 ${start}`);
              const endTime = new Date(`2000-01-01 ${end}`);
              
              if (startTime >= endTime) {
                errors.push(`${category} 类型任务的偏好时间段结束时间必须晚于开始时间`);
              }
            }
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      error: errors.join('; '),
      data: errors.length === 0
    };
  }

  /**
   * 移动到下一步
   */
  moveToNextStep(state: OnboardingState): ApiResponse<OnboardingState> {
    // 验证当前步骤
    const validation = this.validateCurrentStep(state);
    if (!validation.success) {
      return {
        success: false,
        error: validation.error,
        data: state
      };
    }

    // 标记当前步骤为完成
    const newSteps = [...state.steps];
    newSteps[state.currentStep].isCompleted = true;

    // 移动到下一步
    const nextStep = state.currentStep + 1;
    const isCompleted = nextStep >= state.totalSteps;

    const newState: OnboardingState = {
      ...state,
      currentStep: isCompleted ? state.currentStep : nextStep,
      steps: newSteps,
      isCompleted
    };

    return {
      success: true,
      data: newState
    };
  }

  /**
   * 移动到上一步
   */
  moveToPreviousStep(state: OnboardingState): ApiResponse<OnboardingState> {
    if (state.currentStep <= 0) {
      return {
        success: false,
        error: '已经是第一步',
        data: state
      };
    }

    const newState: OnboardingState = {
      ...state,
      currentStep: state.currentStep - 1,
      isCompleted: false
    };

    return {
      success: true,
      data: newState
    };
  }

  /**
   * 跳转到指定步骤
   */
  jumpToStep(state: OnboardingState, stepIndex: number): ApiResponse<OnboardingState> {
    if (stepIndex < 0 || stepIndex >= state.totalSteps) {
      return {
        success: false,
        error: '无效的步骤索引',
        data: state
      };
    }

    const newState: OnboardingState = {
      ...state,
      currentStep: stepIndex,
      isCompleted: false
    };

    return {
      success: true,
      data: newState
    };
  }

  /**
   * 更新时间配置
   */
  updateTimeConfig(state: OnboardingState, updates: Partial<UserTimeConfig>): OnboardingState {
    return {
      ...state,
      timeConfig: {
        ...state.timeConfig,
        ...updates
      }
    };
  }

  /**
   * 完成引导流程
   */
  completeOnboarding(state: OnboardingState): ApiResponse<UserTimeConfig> {
    // 验证所有必需步骤
    const requiredSteps = state.steps.filter(step => step.isRequired);
    const incompleteRequiredSteps = requiredSteps.filter(step => !step.isCompleted);

    if (incompleteRequiredSteps.length > 0) {
      return {
        success: false,
        error: `请完成必需步骤: ${incompleteRequiredSteps.map(s => s.title).join(', ')}`,
        data: null as any
      };
    }

    // 验证最终配置
    const validation = validateTimeConfig(state.timeConfig);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        data: null as any
      };
    }

    // 填充默认值
    const completeConfig: UserTimeConfig = {
      ...DEFAULT_USER_CONFIG,
      ...state.timeConfig
    };

    return {
      success: true,
      data: completeConfig
    };
  }
}
