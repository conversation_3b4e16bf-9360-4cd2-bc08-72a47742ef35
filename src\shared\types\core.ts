/**
 * 核心业务类型定义
 * 这些类型被多个域共享使用
 */

// ============================================================================
// 任务相关类型
// ============================================================================

export interface Task {
  id: string;
  userId: string;
  title: string;
  description?: string;
  category: TaskCategory;
  importance: Priority;
  urgency: Priority;
  deadline: Date;
  estimatedDuration: number; // 分钟
  status: TaskStatus;
  postponeCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export type TaskCategory = 'work' | 'improvement' | 'entertainment';

export type Priority = 1 | 2 | 3 | 4 | 5;

export type TaskStatus = 'pending' | 'in-progress' | 'completed' | 'postponed';

export type Quadrant = 1 | 2 | 3 | 4;

// ============================================================================
// 时间相关类型
// ============================================================================

export interface TimeSlot {
  task: Task;
  startTime: Date;
  endTime: Date;
  isFixed: boolean;
}

export interface DailySchedule {
  date: Date;
  timeSlots: TimeSlot[];
  totalTasks: number;
  estimatedDuration: number;
}

export interface ScoredTask extends Task {
  score: number;
  quadrant: Quadrant;
}

// ============================================================================
// 用户配置类型
// ============================================================================

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  timezone: string;
  workHours: WorkHours;
  timeConfig: UserTimeConfig;
  onboardingCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkHours {
  start: string;
  end: string;
  days?: string[];
}

export interface UserTimeConfig {
  workStart: string;
  workEnd: string;
  workDays: string[];
  sleepStart: string;
  sleepEnd: string;
  fixedSlots: FixedTimeSlot[];
  commuteToWork: number;
  commuteFromWork: number;
  preferredWorkHours: 'morning' | 'afternoon' | 'evening';
  maxContinuousWork: number;
  breakInterval: number;
  categoryPreferences: CategoryPreferences;
}

export interface FixedTimeSlot {
  start: string;
  end: string;
  type: string;
  label: string;
}

export interface CategoryPreferences {
  work: CategoryPreference;
  improvement: CategoryPreference;
  entertainment: CategoryPreference;
}

export interface CategoryPreference {
  preferredTimes: string[];
  maxDaily: number;
}

// ============================================================================
// 分析相关类型
// ============================================================================

export interface CategoryRatios {
  work: number;
  improvement: number;
  entertainment: number;
}

export interface BalanceAnalysis {
  userId: string;
  date: Date;
  categoryRatios: CategoryRatios;
  weeklyStats: WeeklyStats;
  recommendations: string[];
  balanceScore: number;
}

export interface WeeklyStats {
  totalTime: CategoryRatios;
  averageDaily: CategoryRatios;
  trend: 'improving' | 'declining' | 'stable';
}

export interface DailyStats {
  id: string;
  userId: string;
  date: Date;
  workTime: number;
  improvementTime: number;
  entertainmentTime: number;
  tasksCompleted: number;
  tasksPostponed: number;
  balanceScore: number;
}

// ============================================================================
// 算法相关类型
// ============================================================================

export interface PostponedTaskAlert {
  taskId: string;
  task: Task;
  postponeCount: number;
  lastPostponeDate: Date;
  severity: 'low' | 'medium' | 'high';
  recommendation: string;
}

export interface AdjustmentResult {
  success: boolean;
  adjustedSchedule: TimeSlot[];
  affectedTasks: Task[];
  message: string;
  impactScore: number;
}

// ============================================================================
// API 响应类型
// ============================================================================

export interface ApiResponse<T> {
  data: T;
  error?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// ============================================================================
// 事件类型
// ============================================================================

export interface CalendarEvent {
  id?: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  category: string;
  reminders: number[];
}

// ============================================================================
// 错误类型
// ============================================================================

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}
