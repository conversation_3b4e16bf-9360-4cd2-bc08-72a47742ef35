/**
 * 智能规划服务
 * 提供高级的规划服务接口，封装算法复杂性
 */

import {
  Task,
  DailySchedule,
  BalanceAnalysis,
  PostponedTaskAlert,
  AdjustmentResult,
  UserTimeConfig,
  TimeSlot,
  ApiResponse,
  createAppError,
  ERROR_CODES
} from '@/shared';

import { AlgorithmCoordinator } from '../coordinators/AlgorithmCoordinator';

export class PlanningService {
  private algorithmCoordinator: AlgorithmCoordinator;

  constructor() {
    this.algorithmCoordinator = new AlgorithmCoordinator();
  }

  // ============================================================================
  // 每日规划服务
  // ============================================================================

  /**
   * 生成智能每日规划
   */
  async generateDailyPlan(
    userId: string,
    tasks: Task[],
    userTimeConfig?: UserTimeConfig
  ): Promise<ApiResponse<{
    schedule: DailySchedule;
    insights: any;
    recommendations: string[];
  }>> {
    try {
      // 验证输入
      if (!userId) {
        return {
          success: false,
          error: '用户ID不能为空',
          data: null as any
        };
      }

      if (!Array.isArray(tasks)) {
        return {
          success: false,
          error: '任务列表格式不正确',
          data: null as any
        };
      }

      // 生成每日洞察
      const insights = await this.algorithmCoordinator.generateDailyInsights(
        userId,
        tasks,
        userTimeConfig
      );

      return {
        success: true,
        data: {
          schedule: insights.schedule,
          insights: {
            balanceAnalysis: insights.balanceAnalysis,
            postponedAlerts: insights.postponedAlerts,
            stats: this.algorithmCoordinator.getPostponedTasksStats(tasks)
          },
          recommendations: insights.recommendations
        }
      };
    } catch (error) {
      console.error('生成每日规划失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成每日规划时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 快速生成时间安排（简化版）
   */
  async generateQuickSchedule(
    tasks: Task[],
    workHours?: { start: string; end: string }
  ): Promise<ApiResponse<DailySchedule>> {
    try {
      const schedule = this.algorithmCoordinator.planningAlgorithm.generateTimeSlots(
        tasks.map(task => ({ ...task, score: 0, quadrant: 1 as any })),
        workHours || { start: '09:00', end: '18:00' }
      );

      return {
        success: true,
        data: {
          date: new Date(),
          timeSlots: schedule,
          totalTasks: tasks.length,
          estimatedDuration: schedule.reduce((sum, slot) => 
            sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0
          )
        }
      };
    } catch (error) {
      console.error('生成快速安排失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成快速安排时发生错误',
        data: null as any
      };
    }
  }

  // ============================================================================
  // 生活平衡服务
  // ============================================================================

  /**
   * 获取生活平衡分析
   */
  async getBalanceAnalysis(userId: string): Promise<ApiResponse<BalanceAnalysis>> {
    try {
      if (!userId) {
        return {
          success: false,
          error: '用户ID不能为空',
          data: null as any
        };
      }

      const analysis = await this.algorithmCoordinator.analyzeLifeBalance(userId);

      return {
        success: true,
        data: analysis
      };
    } catch (error) {
      console.error('获取生活平衡分析失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取生活平衡分析时发生错误',
        data: null as any
      };
    }
  }

  /**
   * 更新活动统计
   */
  async updateActivityStats(
    userId: string,
    category: 'work' | 'improvement' | 'entertainment',
    duration: number
  ): Promise<ApiResponse<void>> {
    try {
      if (!userId || !category || duration <= 0) {
        return {
          success: false,
          error: '参数不完整或无效',
          data: undefined
        };
      }

      await this.algorithmCoordinator.updateTodayStats(userId, category, duration);

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      console.error('更新活动统计失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新活动统计时发生错误',
        data: undefined
      };
    }
  }

  // ============================================================================
  // 任务修复服务
  // ============================================================================

  /**
   * 获取推迟任务分析
   */
  async getPostponedTasksAnalysis(tasks: Task[]): Promise<ApiResponse<{
    alerts: PostponedTaskAlert[];
    stats: any;
    actionPlan: string[];
  }>> {
    try {
      if (!Array.isArray(tasks)) {
        return {
          success: false,
          error: '任务列表格式不正确',
          data: null as any
        };
      }

      const alerts = this.algorithmCoordinator.analyzePostponedTasks(tasks);
      const stats = this.algorithmCoordinator.getPostponedTasksStats(tasks);
      const actionPlan = this.algorithmCoordinator.generateActionPlan(alerts);

      return {
        success: true,
        data: {
          alerts,
          stats,
          actionPlan
        }
      };
    } catch (error) {
      console.error('获取推迟任务分析失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取推迟任务分析时发生错误',
        data: null as any
      };
    }
  }

  /**
   * 获取任务重新安排建议
   */
  async getTaskRescheduleAdvice(task: Task): Promise<ApiResponse<any>> {
    try {
      if (!task || !task.id) {
        return {
          success: false,
          error: '任务信息不完整',
          data: null as any
        };
      }

      const advice = this.algorithmCoordinator.suggestRescheduleStrategy(task);

      return {
        success: true,
        data: advice
      };
    } catch (error) {
      console.error('获取重新安排建议失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取重新安排建议时发生错误',
        data: null as any
      };
    }
  }

  // ============================================================================
  // 时间调整服务
  // ============================================================================

  /**
   * 处理任务超时
   */
  async handleTaskOverrun(
    overrunTask: Task,
    actualDuration: number,
    currentSchedule: TimeSlot[],
    userTimeConfig?: UserTimeConfig
  ): Promise<ApiResponse<AdjustmentResult>> {
    try {
      // 验证输入
      if (!overrunTask || !overrunTask.id) {
        return {
          success: false,
          error: '超时任务信息不完整',
          data: null as any
        };
      }

      if (actualDuration <= 0) {
        return {
          success: false,
          error: '实际持续时间必须大于0',
          data: null as any
        };
      }

      if (!Array.isArray(currentSchedule)) {
        return {
          success: false,
          error: '当前安排格式不正确',
          data: null as any
        };
      }

      const result = await this.algorithmCoordinator.handleTaskOverrun(
        overrunTask,
        actualDuration,
        currentSchedule,
        userTimeConfig
      );

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('处理任务超时失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '处理任务超时时发生错误',
        data: null as any
      };
    }
  }

  /**
   * 预测时间调整影响
   */
  async predictOverrunImpact(
    overrunMinutes: number,
    schedule: TimeSlot[],
    overrunTaskIndex: number
  ): Promise<ApiResponse<any>> {
    try {
      if (overrunMinutes <= 0) {
        return {
          success: false,
          error: '超时分钟数必须大于0',
          data: null as any
        };
      }

      if (!Array.isArray(schedule) || overrunTaskIndex < 0 || overrunTaskIndex >= schedule.length) {
        return {
          success: false,
          error: '安排信息或任务索引无效',
          data: null as any
        };
      }

      const impact = this.algorithmCoordinator.predictAdjustmentImpact(
        overrunMinutes,
        schedule,
        overrunTaskIndex
      );

      return {
        success: true,
        data: impact
      };
    } catch (error) {
      console.error('预测调整影响失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '预测调整影响时发生错误',
        data: null as any
      };
    }
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 获取任务建议
   */
  getTaskRecommendation(task: Task): string {
    try {
      return this.algorithmCoordinator.getTaskRecommendation(task);
    } catch (error) {
      console.error('获取任务建议失败:', error);
      return '暂无建议';
    }
  }

  /**
   * 获取分类时间建议
   */
  getCategoryAdvice(category: 'work' | 'improvement' | 'entertainment', ratio: number): string {
    try {
      return this.algorithmCoordinator.getCategoryTimeRecommendation(category, ratio);
    } catch (error) {
      console.error('获取分类建议失败:', error);
      return '暂无建议';
    }
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): {
    isHealthy: boolean;
    algorithmStats: any;
    lastUpdate: Date;
  } {
    try {
      return {
        isHealthy: true,
        algorithmStats: this.algorithmCoordinator.getAlgorithmStats(),
        lastUpdate: new Date()
      };
    } catch (error) {
      console.error('获取服务状态失败:', error);
      return {
        isHealthy: false,
        algorithmStats: null,
        lastUpdate: new Date()
      };
    }
  }

  /**
   * 重置服务统计
   */
  resetServiceStats(): void {
    try {
      this.algorithmCoordinator.resetAlgorithmStats();
    } catch (error) {
      console.error('重置服务统计失败:', error);
    }
  }
}
