'use client';

import React, { useState } from 'react';
import { AlertTriangle, Trash2, X } from 'lucide-react';
import { Task } from '@/shared';

interface TaskDeleteConfirmProps {
  task: Task | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (taskId: string) => Promise<void>;
}

export default function TaskDeleteConfirm({ 
  task, 
  isOpen, 
  onClose, 
  onConfirm 
}: TaskDeleteConfirmProps) {
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    if (!task) return;
    
    setLoading(true);
    try {
      await onConfirm(task.id);
      onClose();
    } catch (error) {
      console.error('Failed to delete task:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !task) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">
              删除任务
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-700 mb-4">
            您确定要删除任务 <span className="font-medium text-gray-900">"{task.title}"</span> 吗？
          </p>
          
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <div className="flex">
              <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
              <div className="text-sm text-red-700">
                <p className="font-medium mb-1">此操作无法撤销</p>
                <p>删除后，任务的所有相关数据将永久丢失，包括：</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>任务详细信息</li>
                  <li>历史记录</li>
                  <li>相关统计数据</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Task Info */}
          <div className="bg-gray-50 rounded-md p-3 mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">任务信息</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>标题：</span>
                <span className="font-medium">{task.title}</span>
              </div>
              <div className="flex justify-between">
                <span>分类：</span>
                <span className="font-medium">
                  {task.category === 'work' ? '工作' : 
                   task.category === 'improvement' ? '自我提升' : '娱乐'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>状态：</span>
                <span className="font-medium">
                  {task.status === 'pending' ? '未开始' :
                   task.status === 'in-progress' ? '进行中' :
                   task.status === 'completed' ? '已完成' : '已推迟'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>截止时间：</span>
                <span className="font-medium">
                  {task.deadline.toLocaleDateString('zh-CN')}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            type="button"
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            取消
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                删除中...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                确认删除
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
