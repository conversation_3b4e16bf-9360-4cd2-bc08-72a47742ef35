# TimeManager - 智能时间规划助手

一款基于领域驱动设计(DDD)的企业级智能时间管理应用，采用四大核心算法，帮助用户高效规划时间，保持生活平衡。

## 🎯 核心功能

### 四大智能算法
1. **时间规划算法** - 基于四象限法则和任务类型的智能时间安排
2. **生活平衡算法** - 分析工作、提升、娱乐的时间分配和趋势
3. **任务修复算法** - 检测并修复长期推迟的任务，提供重新安排策略
4. **时间调整算法** - 任务超时时的动态时间重新分配

### 主要特性
- 📊 **智能任务管理**：完整的CRUD操作，支持批量处理和高级过滤
- 🎯 **四象限智能分类**：基于重要性和紧急性的自动任务分类
- ⏰ **智能时间规划**：根据任务类型和用户时间配置生成最优安排
- 📈 **生活平衡分析**：实时监控工作、提升、娱乐时间分配
- 🔧 **任务修复系统**：智能检测推迟任务并提供修复建议
- ⚡ **动态时间调整**：任务超时时自动重新分配时间
- 🕐 **个性化配置**：完整的用户引导和时间偏好设置
- 🔄 **多策略调整**：保守、平衡、激进三种时间调整策略
- 🏗️ **企业级架构**：基于领域驱动设计，高可维护性和可扩展性
- 📱 **响应式设计**：支持多设备，优秀的用户体验

## 🛠️ 技术栈

- **前端框架**: Next.js 14 + TypeScript + Tailwind CSS
- **架构设计**: 领域驱动设计 (DDD) + 分层架构
- **状态管理**: Zustand + 响应式状态
- **数据库**: Supabase (PostgreSQL + 实时功能)
- **认证系统**: Supabase Auth + 用户配置管理
- **UI组件**: Lucide React + Radix UI + 自定义组件
- **类型安全**: 完整的 TypeScript 类型系统
- **部署平台**: Vercel + 自动化部署

## 🚀 快速开始

### 1. 环境要求
- Node.js 18+
- npm 或 yarn

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制 `.env.local` 文件并配置以下环境变量：

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_DATABASE_NAME = TimeManager;
SUPABASE_DATABASE_PASSWORD = 4kVJulB4ZRXwcuHx

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=TimeManager
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
timemanager/
├── src/
│   ├── app/                           # Next.js App Router
│   │   ├── auth/                     # 认证页面
│   │   ├── dashboard/                # 主控制台
│   │   ├── tasks/                    # 任务管理
│   │   └── onboarding/              # 用户引导
│   ├── domains/                      # 业务域（DDD架构）
│   │   ├── intelligent-planning/     # 智能规划域
│   │   │   ├── algorithms/          # 四大核心算法
│   │   │   ├── coordinators/        # 算法协调器
│   │   │   └── services/           # 规划服务
│   │   ├── task-management/         # 任务管理域
│   │   │   ├── models/             # 任务模型
│   │   │   ├── repositories/       # 数据仓储
│   │   │   └── services/           # 任务服务
│   │   └── user-config/            # 用户配置域
│   │       ├── models/             # 用户模型
│   │       ├── repositories/       # 配置仓储
│   │       └── services/           # 配置和引导服务
│   ├── shared/                      # 共享模块
│   │   ├── types/                  # 核心类型定义
│   │   ├── constants/              # 应用常量
│   │   ├── utils/                  # 工具函数
│   │   └── components/             # 共享组件
│   ├── lib/                        # 基础设施
│   │   └── supabase.ts            # 数据库配置
│   └── store/                      # 状态管理
└── public/                         # 静态资源
```

## 🧮 核心算法详解

### 1. 时间规划算法 (PlanningAlgorithm)
- **智能评分**: `重要性 × 0.6 + 紧急性 × 0.4 + 分类权重 + 推迟惩罚`
- **四象限分类**: 自动将任务分配到合适的象限
- **类型感知调度**: 根据任务类型（工作/提升/娱乐）安排合适时间段
- **时间约束**: 考虑用户工作时间、睡眠时间、固定时间段

### 2. 生活平衡算法 (BalanceAlgorithm)
- **理想比例**: 工作60% + 提升25% + 娱乐15%
- **动态分析**: 实时计算当前时间分配和趋势
- **平衡评分**: 基于偏差计算0-100分的平衡分数
- **智能建议**: 根据分析结果提供个性化调整建议

### 3. 任务修复算法 (FixAlgorithm)
- **推迟检测**: 智能识别长期被推迟的任务
- **严重程度评估**: 综合推迟次数、截止时间、重要性等因素
- **修复策略**: 提供重新安排、分解任务、调整优先级等解决方案
- **预防机制**: 主动预警可能被推迟的任务

### 4. 时间调整算法 (TimeAdjustmentAlgorithm)
- **超时检测**: 实时监控任务执行时间
- **动态调整**: 任务超时时自动重新分配后续时间
- **多种策略**: 压缩后续任务、推迟非紧急任务、重新安排时间段
- **影响评估**: 预测调整对整体计划的影响

## 🏗️ 架构设计

### 领域驱动设计 (DDD)
项目采用领域驱动设计，将复杂的业务逻辑分解为四个核心域：

#### 1. 智能规划域 (Intelligent Planning)
- **职责**: 时间规划、生活平衡分析、任务修复、时间调整
- **核心组件**: 四大算法 + 算法协调器 + 规划服务
- **特点**: 高度解耦的算法实现，支持独立测试和优化

#### 2. 任务管理域 (Task Management)
- **职责**: 任务的完整生命周期管理
- **核心组件**: 任务模型 + 仓储层 + 业务服务 + 验证服务
- **特点**: 完整的CRUD操作，支持批量处理和高级查询

#### 3. 用户配置域 (User Config)
- **职责**: 用户配置管理、引导流程、个性化设置
- **核心组件**: 配置模型 + 引导服务 + 偏好管理
- **特点**: 智能引导流程，支持配置验证和导入导出

#### 4. 共享模块 (Shared)
- **职责**: 跨域的类型定义、工具函数、常量
- **核心组件**: 核心类型 + 工具函数 + 应用常量
- **特点**: 统一的类型系统，避免重复代码

### 分层架构
```
┌─────────────────────────────────────┐
│           表现层 (UI Layer)          │
│     Next.js Pages + Components      │
├─────────────────────────────────────┤
│          应用层 (App Layer)          │
│        Zustand Stores + Hooks       │
├─────────────────────────────────────┤
│         业务层 (Domain Layer)        │
│    Services + Algorithms + Models   │
├─────────────────────────────────────┤
│        基础设施层 (Infra Layer)       │
│     Repositories + Supabase API     │
└─────────────────────────────────────┘
```

## 📝 开发状态

### 已完成 ✅

#### 核心架构
- [x] **领域驱动设计重构**：完整的DDD架构实现
- [x] **分层架构设计**：清晰的职责分离和模块化
- [x] **类型安全系统**：完整的TypeScript类型定义

#### 智能规划域
- [x] **四大核心算法**：时间规划、生活平衡、任务修复、时间调整
- [x] **算法协调器**：统一的算法管理和调度
- [x] **规划服务层**：高级业务逻辑封装

#### 任务管理域
- [x] **完整CRUD系统**：任务的全生命周期管理
- [x] **数据验证引擎**：多层次验证和业务规则检查
- [x] **高级查询功能**：过滤、排序、分页、统计
- [x] **批量操作支持**：批量更新、删除、状态管理

#### 用户配置域
- [x] **智能引导流程**：7步完整的用户配置向导
- [x] **配置管理系统**：时间偏好、通知设置、个性化配置
- [x] **配置验证系统**：实时验证和智能建议
- [x] **导入导出功能**：配置备份和恢复

#### 用户界面
- [x] **响应式设计**：支持多设备的现代化界面
- [x] **四象限选择器**：可视化的任务优先级设置
- [x] **简化任务创建**：优化的用户体验

### 下一步 📋

#### 功能增强
- [ ] **实时协作功能**：多用户任务协作和共享
- [ ] **日历集成**：与外部日历系统同步
- [ ] **智能通知系统**：基于用户行为的智能提醒
- [ ] **数据分析面板**：详细的时间使用分析和报告

#### 技术优化
- [ ] **性能优化**：算法性能调优和缓存策略
- [ ] **离线支持**：PWA功能和离线数据同步
- [ ] **桌面应用**：Tauri桌面客户端
- [ ] **移动应用**：React Native移动端

#### 企业功能
- [ ] **团队管理**：企业级用户和权限管理
- [ ] **API开放**：RESTful API和SDK
- [ ] **数据导出**：多格式数据导出和集成
- [ ] **高级分析**：机器学习驱动的时间优化建议

## ✨ 特性亮点

### 🧠 智能化
- **自适应算法**：根据用户行为自动优化时间安排
- **智能建议**：基于历史数据提供个性化建议
- **预测分析**：预测任务完成时间和潜在冲突

### 🎯 个性化
- **完整引导流程**：新用户7步配置向导
- **灵活配置**：支持复杂的时间偏好和工作模式
- **多策略支持**：保守、平衡、激进三种调整策略

### 🏗️ 企业级
- **高可维护性**：清晰的架构设计和代码组织
- **高可扩展性**：模块化设计，易于添加新功能
- **高可靠性**：完整的错误处理和数据验证

### 🔧 开发友好
- **类型安全**：完整的TypeScript类型系统
- **测试友好**：解耦的架构设计便于单元测试
- **文档完善**：详细的代码注释和API文档

## 🚀 部署

项目可以轻松部署到 Vercel：

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### 环境变量配置
确保在 Vercel 中配置以下环境变量：
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `NEXT_PUBLIC_APP_URL`
- `NEXT_PUBLIC_APP_NAME`

## 📄 许可证

MIT License
