/**
 * 任务相关的工具函数
 */

import { Task, TaskCategory, Priority, Quadrant, TaskStatus } from '../types/core';
import { QUADRANTS, TASK_CATEGORIES, PRIORITY_LEVELS } from '../constants';

// ============================================================================
// 四象限分类
// ============================================================================

/**
 * 根据重要性和紧急性确定四象限
 */
export function classifyQuadrant(importance: Priority, urgency: Priority): Quadrant {
  if (importance >= 4 && urgency >= 4) {
    return QUADRANTS.URGENT_IMPORTANT;
  } else if (importance >= 4 && urgency < 4) {
    return QUADRANTS.IMPORTANT_NOT_URGENT;
  } else if (importance < 4 && urgency >= 4) {
    return QUADRANTS.URGENT_NOT_IMPORTANT;
  } else {
    return QUADRANTS.NOT_URGENT_NOT_IMPORTANT;
  }
}

/**
 * 获取四象限的描述
 */
export function getQuadrantDescription(quadrant: Quadrant): string {
  const descriptions = {
    [QUADRANTS.URGENT_IMPORTANT]: '重要且紧急 - 立即执行',
    [QUADRANTS.IMPORTANT_NOT_URGENT]: '重要不紧急 - 计划执行',
    [QUADRANTS.URGENT_NOT_IMPORTANT]: '不重要但紧急 - 委托处理',
    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: '不重要不紧急 - 减少或删除'
  };
  return descriptions[quadrant];
}

/**
 * 获取四象限的简短标题
 */
export function getQuadrantTitle(quadrant: Quadrant): string {
  const titles = {
    [QUADRANTS.URGENT_IMPORTANT]: '象限 I',
    [QUADRANTS.IMPORTANT_NOT_URGENT]: '象限 II', 
    [QUADRANTS.URGENT_NOT_IMPORTANT]: '象限 III',
    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: '象限 IV'
  };
  return titles[quadrant];
}

// ============================================================================
// 任务分类
// ============================================================================

/**
 * 获取任务分类的中文名称
 */
export function getCategoryName(category: TaskCategory): string {
  const names = {
    [TASK_CATEGORIES.WORK]: '工作',
    [TASK_CATEGORIES.IMPROVEMENT]: '提升',
    [TASK_CATEGORIES.ENTERTAINMENT]: '娱乐'
  };
  return names[category];
}

/**
 * 获取任务分类的颜色主题
 */
export function getCategoryColor(category: TaskCategory): string {
  const colors = {
    [TASK_CATEGORIES.WORK]: 'blue',
    [TASK_CATEGORIES.IMPROVEMENT]: 'green',
    [TASK_CATEGORIES.ENTERTAINMENT]: 'purple'
  };
  return colors[category];
}

/**
 * 获取任务分类的图标
 */
export function getCategoryIcon(category: TaskCategory): string {
  const icons = {
    [TASK_CATEGORIES.WORK]: '💼',
    [TASK_CATEGORIES.IMPROVEMENT]: '📚',
    [TASK_CATEGORIES.ENTERTAINMENT]: '🎮'
  };
  return icons[category];
}

// ============================================================================
// 任务状态
// ============================================================================

/**
 * 获取任务状态的中文名称
 */
export function getStatusName(status: TaskStatus): string {
  const names = {
    pending: '待处理',
    'in-progress': '进行中',
    completed: '已完成',
    postponed: '已推迟'
  };
  return names[status];
}

/**
 * 获取任务状态的颜色
 */
export function getStatusColor(status: TaskStatus): string {
  const colors = {
    pending: 'gray',
    'in-progress': 'blue',
    completed: 'green',
    postponed: 'yellow'
  };
  return colors[status];
}

/**
 * 检查任务是否可以开始
 */
export function canStartTask(task: Task): boolean {
  return task.status === 'pending' || task.status === 'postponed';
}

/**
 * 检查任务是否已完成
 */
export function isTaskCompleted(task: Task): boolean {
  return task.status === 'completed';
}

/**
 * 检查任务是否进行中
 */
export function isTaskInProgress(task: Task): boolean {
  return task.status === 'in-progress';
}

// ============================================================================
// 优先级
// ============================================================================

/**
 * 获取优先级的中文名称
 */
export function getPriorityName(priority: Priority): string {
  const names = {
    [PRIORITY_LEVELS.VERY_LOW]: '很低',
    [PRIORITY_LEVELS.LOW]: '较低',
    [PRIORITY_LEVELS.MEDIUM]: '中等',
    [PRIORITY_LEVELS.HIGH]: '较高',
    [PRIORITY_LEVELS.VERY_HIGH]: '很高'
  };
  return names[priority];
}

/**
 * 获取优先级的颜色
 */
export function getPriorityColor(priority: Priority): string {
  const colors = {
    [PRIORITY_LEVELS.VERY_LOW]: 'gray',
    [PRIORITY_LEVELS.LOW]: 'blue',
    [PRIORITY_LEVELS.MEDIUM]: 'yellow',
    [PRIORITY_LEVELS.HIGH]: 'orange',
    [PRIORITY_LEVELS.VERY_HIGH]: 'red'
  };
  return colors[priority];
}

// ============================================================================
// 任务过滤和排序
// ============================================================================

/**
 * 按分类过滤任务
 */
export function filterTasksByCategory(tasks: Task[], category: TaskCategory): Task[] {
  return tasks.filter(task => task.category === category);
}

/**
 * 按状态过滤任务
 */
export function filterTasksByStatus(tasks: Task[], status: TaskStatus): Task[] {
  return tasks.filter(task => task.status === status);
}

/**
 * 按四象限过滤任务
 */
export function filterTasksByQuadrant(tasks: Task[], quadrant: Quadrant): Task[] {
  return tasks.filter(task => classifyQuadrant(task.importance, task.urgency) === quadrant);
}

/**
 * 获取今日需要处理的任务
 */
export function getTodayTasks(tasks: Task[]): Task[] {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  return tasks.filter(task => {
    // 包含今日截止的任务和未完成的高优先级任务
    const isToday = task.deadline <= tomorrow;
    const isHighPriority = task.importance >= 4 || task.urgency >= 4;
    const isPending = task.status === 'pending' || task.status === 'in-progress';
    
    return isPending && (isToday || isHighPriority);
  });
}

/**
 * 按优先级排序任务
 */
export function sortTasksByPriority(tasks: Task[]): Task[] {
  return [...tasks].sort((a, b) => {
    const aQuadrant = classifyQuadrant(a.importance, a.urgency);
    const bQuadrant = classifyQuadrant(b.importance, b.urgency);
    
    // 先按象限排序
    if (aQuadrant !== bQuadrant) {
      return aQuadrant - bQuadrant;
    }
    
    // 再按重要性排序
    if (a.importance !== b.importance) {
      return b.importance - a.importance;
    }
    
    // 最后按紧急性排序
    return b.urgency - a.urgency;
  });
}

/**
 * 按截止时间排序任务
 */
export function sortTasksByDeadline(tasks: Task[]): Task[] {
  return [...tasks].sort((a, b) => a.deadline.getTime() - b.deadline.getTime());
}

// ============================================================================
// 任务验证
// ============================================================================

/**
 * 验证任务数据的完整性
 */
export function validateTask(task: Partial<Task>): string[] {
  const errors: string[] = [];
  
  if (!task.title || task.title.trim().length === 0) {
    errors.push('任务标题不能为空');
  }
  
  if (task.title && task.title.length > 100) {
    errors.push('任务标题不能超过100个字符');
  }
  
  if (!task.category || !Object.values(TASK_CATEGORIES).includes(task.category as TaskCategory)) {
    errors.push('请选择有效的任务分类');
  }
  
  if (!task.importance || task.importance < 1 || task.importance > 5) {
    errors.push('重要性必须在1-5之间');
  }
  
  if (!task.urgency || task.urgency < 1 || task.urgency > 5) {
    errors.push('紧急性必须在1-5之间');
  }
  
  if (!task.deadline) {
    errors.push('请设置截止时间');
  } else if (task.deadline < new Date()) {
    errors.push('截止时间不能早于当前时间');
  }
  
  if (!task.estimatedDuration || task.estimatedDuration < 15 || task.estimatedDuration > 480) {
    errors.push('预估时长必须在15分钟到8小时之间');
  }
  
  return errors;
}

/**
 * 检查任务是否即将到期
 */
export function isTaskDueSoon(task: Task, hoursThreshold: number = 24): boolean {
  const now = new Date();
  const timeDiff = task.deadline.getTime() - now.getTime();
  const hoursDiff = timeDiff / (1000 * 60 * 60);
  
  return hoursDiff > 0 && hoursDiff <= hoursThreshold;
}

/**
 * 检查任务是否已过期
 */
export function isTaskOverdue(task: Task): boolean {
  return task.deadline < new Date() && task.status !== 'completed';
}

/**
 * 获取任务的紧急程度描述
 */
export function getTaskUrgencyDescription(task: Task): string {
  if (isTaskOverdue(task)) {
    return '已过期';
  } else if (isTaskDueSoon(task, 2)) {
    return '2小时内到期';
  } else if (isTaskDueSoon(task, 24)) {
    return '今日到期';
  } else if (isTaskDueSoon(task, 48)) {
    return '明日到期';
  } else {
    return '充足时间';
  }
}
