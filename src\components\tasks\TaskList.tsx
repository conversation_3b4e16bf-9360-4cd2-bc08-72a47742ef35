'use client';

import React, { useState, useMemo } from 'react';
import { Search, Filter, Plus, SortAsc, SortDesc } from 'lucide-react';
import { Task, TaskStatus, TaskCategory } from '@/shared';
import TaskItem from './TaskItem';
import TaskEditForm from './TaskEditForm';
import TaskDeleteConfirm from './TaskDeleteConfirm';

interface TaskListProps {
  tasks: Task[];
  loading?: boolean;
  onCreateTask: () => void;
  onEditTask: (taskId: string, updates: Partial<Task>) => Promise<void>;
  onDeleteTask: (taskId: string) => Promise<void>;
  onStatusChange: (taskId: string, status: TaskStatus) => Promise<void>;
  onStartTask: (taskId: string) => Promise<void>;
  onCompleteTask: (taskId: string) => Promise<void>;
}

type SortOption = 'deadline' | 'priority' | 'created' | 'title';
type SortDirection = 'asc' | 'desc';

interface FilterOptions {
  status: TaskStatus | 'all';
  category: TaskCategory | 'all';
  search: string;
}

export default function TaskList({
  tasks,
  loading = false,
  onCreateTask,
  onEditTask,
  onDeleteTask,
  onStatusChange,
  onStartTask,
  onCompleteTask
}: TaskListProps) {
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [deletingTask, setDeletingTask] = useState<Task | null>(null);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  const [filters, setFilters] = useState<FilterOptions>({
    status: 'all',
    category: 'all',
    search: ''
  });
  
  const [sortBy, setSortBy] = useState<SortOption>('deadline');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [showFilters, setShowFilters] = useState(false);

  // 过滤和排序任务
  const filteredAndSortedTasks = useMemo(() => {
    let filtered = tasks.filter(task => {
      // 状态过滤
      if (filters.status !== 'all' && task.status !== filters.status) {
        return false;
      }
      
      // 分类过滤
      if (filters.category !== 'all' && task.category !== filters.category) {
        return false;
      }
      
      // 搜索过滤
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return task.title.toLowerCase().includes(searchLower) ||
               (task.description && task.description.toLowerCase().includes(searchLower));
      }
      
      return true;
    });

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'deadline':
          comparison = a.deadline.getTime() - b.deadline.getTime();
          break;
        case 'priority':
          // 按重要性和紧急性的综合分数排序
          const scoreA = a.importance * 2 + a.urgency;
          const scoreB = b.importance * 2 + b.urgency;
          comparison = scoreB - scoreA;
          break;
        case 'created':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [tasks, filters, sortBy, sortDirection]);

  const handleEdit = (task: Task) => {
    setEditingTask(task);
    setShowEditForm(true);
  };

  const handleDelete = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      setDeletingTask(task);
      setShowDeleteConfirm(true);
    }
  };

  const handleSaveEdit = async (updates: Partial<Task>) => {
    if (editingTask) {
      await onEditTask(editingTask.id, updates);
    }
  };

  const handleSort = (option: SortOption) => {
    if (sortBy === option) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(option);
      setSortDirection('asc');
    }
  };

  const getStatusCount = (status: TaskStatus) => {
    return tasks.filter(task => task.status === status).length;
  };

  const getCategoryCount = (category: TaskCategory) => {
    return tasks.filter(task => task.category === category).length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务管理</h1>
          <p className="text-gray-600">共 {tasks.length} 个任务，{filteredAndSortedTasks.length} 个符合条件</p>
        </div>
        <button
          onClick={onCreateTask}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 gap-2"
        >
          <Plus className="h-4 w-4" />
          新建任务
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索任务标题或描述..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 gap-2"
          >
            <Filter className="h-4 w-4" />
            筛选
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as TaskStatus | 'all' }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">全部 ({tasks.length})</option>
                <option value="pending">未开始 ({getStatusCount('pending')})</option>
                <option value="in-progress">进行中 ({getStatusCount('in-progress')})</option>
                <option value="completed">已完成 ({getStatusCount('completed')})</option>
                <option value="postponed">已推迟 ({getStatusCount('postponed')})</option>
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">分类</label>
              <select
                value={filters.category}
                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value as TaskCategory | 'all' }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">全部 ({tasks.length})</option>
                <option value="work">工作 ({getCategoryCount('work')})</option>
                <option value="improvement">自我提升 ({getCategoryCount('improvement')})</option>
                <option value="entertainment">娱乐 ({getCategoryCount('entertainment')})</option>
              </select>
            </div>

            {/* Sort Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">排序</label>
              <div className="flex gap-1">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortOption)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="deadline">截止时间</option>
                  <option value="priority">优先级</option>
                  <option value="created">创建时间</option>
                  <option value="title">标题</option>
                </select>
                <button
                  onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                  className="px-2 py-2 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-50"
                >
                  {sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <button
                onClick={() => {
                  setFilters({ status: 'all', category: 'all', search: '' });
                  setSortBy('deadline');
                  setSortDirection('asc');
                }}
                className="w-full px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                清除筛选
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Task List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">正在加载任务...</p>
          </div>
        ) : filteredAndSortedTasks.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow">
            <p className="text-gray-500 text-lg">
              {filters.search || filters.status !== 'all' || filters.category !== 'all' 
                ? '没有找到符合条件的任务' 
                : '还没有任务，点击上方按钮创建第一个任务吧！'}
            </p>
          </div>
        ) : (
          filteredAndSortedTasks.map(task => (
            <TaskItem
              key={task.id}
              task={task}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onStatusChange={onStatusChange}
              onStart={onStartTask}
              onComplete={onCompleteTask}
            />
          ))
        )}
      </div>

      {/* Edit Form Modal */}
      <TaskEditForm
        task={editingTask}
        isOpen={showEditForm}
        onClose={() => {
          setShowEditForm(false);
          setEditingTask(null);
        }}
        onSave={handleSaveEdit}
      />

      {/* Delete Confirmation Modal */}
      <TaskDeleteConfirm
        task={deletingTask}
        isOpen={showDeleteConfirm}
        onClose={() => {
          setShowDeleteConfirm(false);
          setDeletingTask(null);
        }}
        onConfirm={onDeleteTask}
      />
    </div>
  );
}
