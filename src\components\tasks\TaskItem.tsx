'use client';

import React, { useState } from 'react';
import { 
  Clock, 
  Calendar, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  CheckCircle,
  AlertCircle,
  MoreVertical
} from 'lucide-react';
import { Task } from '@/shared';
import { 
  getCategoryColor, 
  getCategoryIcon, 
  getCategoryName,
  getStatusColor,
  getStatusName,
  getPriorityColor,
  isTaskOverdue,
  isTaskDueSoon
} from '@/shared/utils/taskUtils';

interface TaskItemProps {
  task: Task;
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onStatusChange: (taskId: string, status: Task['status']) => void;
  onStart: (taskId: string) => void;
  onComplete: (taskId: string) => void;
}

export default function TaskItem({ 
  task, 
  onEdit, 
  onDelete, 
  onStatusChange, 
  onStart, 
  onComplete 
}: TaskItemProps) {
  const [showActions, setShowActions] = useState(false);
  
  const categoryColor = getCategoryColor(task.category);
  const statusColor = getStatusColor(task.status);
  const isOverdue = isTaskOverdue(task);
  const isDueSoon = isTaskDueSoon(task);
  
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatDeadline = (date: Date) => {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `逾期 ${Math.abs(diffDays)} 天`;
    } else if (diffDays === 0) {
      return '今天到期';
    } else if (diffDays === 1) {
      return '明天到期';
    } else if (diffDays <= 7) {
      return `${diffDays} 天后到期`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  const getQuadrantInfo = (importance: number, urgency: number) => {
    if (importance >= 4 && urgency >= 4) {
      return { label: 'Q1', color: 'bg-red-100 text-red-800', desc: '重要紧急' };
    } else if (importance >= 4 && urgency < 4) {
      return { label: 'Q2', color: 'bg-blue-100 text-blue-800', desc: '重要不紧急' };
    } else if (importance < 4 && urgency >= 4) {
      return { label: 'Q3', color: 'bg-yellow-100 text-yellow-800', desc: '不重要紧急' };
    } else {
      return { label: 'Q4', color: 'bg-gray-100 text-gray-800', desc: '不重要不紧急' };
    }
  };

  const quadrant = getQuadrantInfo(task.importance, task.urgency);

  const handleStatusChange = (newStatus: Task['status']) => {
    if (newStatus === 'completed') {
      onComplete(task.id);
    } else if (newStatus === 'in-progress') {
      onStart(task.id);
    } else {
      onStatusChange(task.id, newStatus);
    }
  };

  return (
    <div className={`
      bg-white rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow duration-200
      ${isOverdue ? 'border-l-red-500 bg-red-50' : 
        isDueSoon ? 'border-l-yellow-500 bg-yellow-50' : 
        `border-l-${categoryColor}-500`}
    `}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-lg font-medium text-gray-900 truncate">
                {task.title}
              </h3>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${quadrant.color}`}>
                {quadrant.label}
              </span>
            </div>
            {task.description && (
              <p className="text-sm text-gray-600 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
          
          {/* Actions Menu */}
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <MoreVertical className="h-4 w-4 text-gray-500" />
            </button>
            
            {showActions && (
              <div className="absolute right-0 top-8 bg-white rounded-md shadow-lg border z-10 min-w-[120px]">
                <button
                  onClick={() => {
                    onEdit(task);
                    setShowActions(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                >
                  <Edit className="h-3 w-3" />
                  编辑
                </button>
                <button
                  onClick={() => {
                    onDelete(task.id);
                    setShowActions(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
                >
                  <Trash2 className="h-3 w-3" />
                  删除
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Task Info */}
        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
          <div className="flex items-center gap-1">
            <span className={`w-2 h-2 rounded-full bg-${categoryColor}-500`}></span>
            <span>{getCategoryName(task.category)}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{formatDuration(task.estimatedDuration)}</span>
          </div>
          
          <div className={`flex items-center gap-1 ${isOverdue ? 'text-red-600' : isDueSoon ? 'text-yellow-600' : ''}`}>
            <Calendar className="h-3 w-3" />
            <span>{formatDeadline(task.deadline)}</span>
            {isOverdue && <AlertCircle className="h-3 w-3" />}
          </div>
        </div>

        {/* Status and Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColor}`}>
              {getStatusName(task.status)}
            </span>
            {task.postponeCount > 0 && (
              <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                推迟 {task.postponeCount} 次
              </span>
            )}
          </div>
          
          {/* Quick Actions */}
          <div className="flex items-center gap-1">
            {task.status === 'pending' && (
              <button
                onClick={() => handleStatusChange('in-progress')}
                className="p-1 rounded-full hover:bg-green-100 text-green-600 transition-colors"
                title="开始任务"
              >
                <Play className="h-4 w-4" />
              </button>
            )}
            
            {task.status === 'in-progress' && (
              <>
                <button
                  onClick={() => handleStatusChange('pending')}
                  className="p-1 rounded-full hover:bg-yellow-100 text-yellow-600 transition-colors"
                  title="暂停任务"
                >
                  <Pause className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleStatusChange('completed')}
                  className="p-1 rounded-full hover:bg-blue-100 text-blue-600 transition-colors"
                  title="完成任务"
                >
                  <CheckCircle className="h-4 w-4" />
                </button>
              </>
            )}
            
            {task.status === 'completed' && (
              <button
                onClick={() => handleStatusChange('pending')}
                className="p-1 rounded-full hover:bg-gray-100 text-gray-600 transition-colors"
                title="重新激活"
              >
                <Play className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
