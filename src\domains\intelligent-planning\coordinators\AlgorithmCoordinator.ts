/**
 * 算法协调器
 * 统一管理和协调所有智能规划算法
 */

import {
  Task,
  DailySchedule,
  BalanceAnalysis,
  PostponedTaskAlert,
  AdjustmentResult,
  UserTimeConfig,
  TimeSlot
} from '@/shared';

import { PlanningAlgorithm } from '../algorithms/PlanningAlgorithm';
import { BalanceAlgorithm } from '../algorithms/BalanceAlgorithm';
import { FixAlgorithm } from '../algorithms/FixAlgorithm';
import { TimeAdjustmentAlgorithm } from '../algorithms/TimeAdjustmentAlgorithm';

export class AlgorithmCoordinator {
  private planningAlgorithm: PlanningAlgorithm;
  private balanceAlgorithm: BalanceAlgorithm;
  private fixAlgorithm: FixAlgorithm;
  private timeAdjustmentAlgorithm: TimeAdjustmentAlgorithm;

  constructor() {
    this.planningAlgorithm = new PlanningAlgorithm();
    this.balanceAlgorithm = new BalanceAlgorithm();
    this.fixAlgorithm = new FixAlgorithm();
    this.timeAdjustmentAlgorithm = new TimeAdjustmentAlgorithm();
  }

  // ============================================================================
  // 时间规划相关方法
  // ============================================================================

  /**
   * 生成智能的每日时间安排
   */
  async generateDailySchedule(
    tasks: Task[], 
    userTimeConfig?: UserTimeConfig
  ): Promise<DailySchedule> {
    try {
      // 1. 检查推迟任务并给出警告
      const postponedAlerts = this.fixAlgorithm.analyzePostponedTasks(tasks);
      const urgentAlerts = postponedAlerts.filter(alert => 
        this.fixAlgorithm.needsUrgentAttention(alert.task)
      );

      // 2. 生成基础时间安排
      const schedule = this.planningAlgorithm.generateDailySchedule(tasks, userTimeConfig);

      // 3. 如果有紧急推迟任务，调整安排优先级
      if (urgentAlerts.length > 0) {
        console.log(`检测到 ${urgentAlerts.length} 个需要紧急处理的推迟任务`);
        // 这里可以进一步优化安排逻辑
      }

      return schedule;
    } catch (error) {
      console.error('生成每日安排时出错:', error);
      throw new Error('无法生成每日时间安排');
    }
  }

  /**
   * 获取任务建议
   */
  getTaskRecommendation(task: Task): string {
    return this.planningAlgorithm.getTaskRecommendation(task);
  }

  // ============================================================================
  // 生活平衡分析相关方法
  // ============================================================================

  /**
   * 分析用户的生活平衡状况
   */
  async analyzeLifeBalance(userId: string, date?: Date): Promise<BalanceAnalysis> {
    try {
      return await this.balanceAlgorithm.analyzeBalance(userId, date);
    } catch (error) {
      console.error('分析生活平衡时出错:', error);
      throw new Error('无法分析生活平衡状况');
    }
  }

  /**
   * 更新今日统计数据
   */
  async updateTodayStats(userId: string, category: any, duration: number): Promise<void> {
    try {
      await this.balanceAlgorithm.updateTodayStats(userId, category, duration);
    } catch (error) {
      console.error('更新统计数据时出错:', error);
      throw new Error('无法更新统计数据');
    }
  }

  /**
   * 获取分类时间建议
   */
  getCategoryTimeRecommendation(category: any, currentRatio: number): string {
    return this.balanceAlgorithm.getCategoryTimeRecommendation(category, currentRatio);
  }

  // ============================================================================
  // 任务修复相关方法
  // ============================================================================

  /**
   * 分析推迟的任务
   */
  analyzePostponedTasks(tasks: Task[]): PostponedTaskAlert[] {
    return this.fixAlgorithm.analyzePostponedTasks(tasks);
  }

  /**
   * 获取推迟任务统计
   */
  getPostponedTasksStats(tasks: Task[]): any {
    return this.fixAlgorithm.getPostponedTasksStats(tasks);
  }

  /**
   * 建议任务重新安排策略
   */
  suggestRescheduleStrategy(task: Task): any {
    return this.fixAlgorithm.suggestRescheduleStrategy(task);
  }

  /**
   * 生成修复行动计划
   */
  generateActionPlan(alerts: PostponedTaskAlert[]): string[] {
    return this.fixAlgorithm.generateActionPlan(alerts);
  }

  // ============================================================================
  // 时间调整相关方法
  // ============================================================================

  /**
   * 处理任务超时调整
   */
  async handleTaskOverrun(
    overrunTask: Task,
    actualDuration: number,
    currentSchedule: TimeSlot[],
    userTimeConfig?: UserTimeConfig
  ): Promise<AdjustmentResult> {
    try {
      const result = this.timeAdjustmentAlgorithm.adjustForOverrun(
        overrunTask,
        actualDuration,
        currentSchedule,
        userTimeConfig
      );

      // 如果调整成功，更新统计数据
      if (result.success) {
        await this.balanceAlgorithm.updateTodayStats(
          overrunTask.userId,
          overrunTask.category,
          actualDuration
        );
      }

      return result;
    } catch (error) {
      console.error('处理任务超时时出错:', error);
      throw new Error('无法处理任务超时调整');
    }
  }

  /**
   * 预测调整影响
   */
  predictAdjustmentImpact(
    overrunMinutes: number,
    schedule: TimeSlot[],
    overrunIndex: number
  ): any {
    return this.timeAdjustmentAlgorithm.predictAdjustmentImpact(
      overrunMinutes,
      schedule,
      overrunIndex
    );
  }

  // ============================================================================
  // 综合分析和建议
  // ============================================================================

  /**
   * 生成综合的每日建议
   */
  async generateDailyInsights(
    userId: string,
    tasks: Task[],
    userTimeConfig?: UserTimeConfig
  ): Promise<{
    schedule: DailySchedule;
    balanceAnalysis: BalanceAnalysis;
    postponedAlerts: PostponedTaskAlert[];
    recommendations: string[];
  }> {
    try {
      // 并行执行多个分析
      const [schedule, balanceAnalysis, postponedAlerts] = await Promise.all([
        this.generateDailySchedule(tasks, userTimeConfig),
        this.analyzeLifeBalance(userId),
        Promise.resolve(this.analyzePostponedTasks(tasks))
      ]);

      // 生成综合建议
      const recommendations = this.generateComprehensiveRecommendations(
        schedule,
        balanceAnalysis,
        postponedAlerts
      );

      return {
        schedule,
        balanceAnalysis,
        postponedAlerts,
        recommendations
      };
    } catch (error) {
      console.error('生成每日洞察时出错:', error);
      throw new Error('无法生成每日洞察');
    }
  }

  /**
   * 生成综合建议
   */
  private generateComprehensiveRecommendations(
    schedule: DailySchedule,
    balanceAnalysis: BalanceAnalysis,
    postponedAlerts: PostponedTaskAlert[]
  ): string[] {
    const recommendations: string[] = [];

    // 基于时间安排的建议
    if (schedule.timeSlots.length === 0) {
      recommendations.push('📅 今日暂无安排的任务，可以处理一些推迟的任务或进行个人提升');
    } else if (schedule.estimatedDuration > 480) { // 超过8小时
      recommendations.push('⏰ 今日安排较满，注意劳逸结合，适当休息');
    }

    // 基于生活平衡的建议
    if (balanceAnalysis.balanceScore < 60) {
      recommendations.push('⚖️ 生活平衡需要调整，' + balanceAnalysis.recommendations[0]);
    }

    // 基于推迟任务的建议
    const urgentAlerts = postponedAlerts.filter(alert => alert.severity === 'high');
    if (urgentAlerts.length > 0) {
      recommendations.push(`🚨 有 ${urgentAlerts.length} 个高优先级推迟任务需要立即处理`);
    }

    // 基于任务分布的建议
    const categoryDistribution = this.analyzeCategoryDistribution(schedule);
    if (categoryDistribution.work > 0.8) {
      recommendations.push('💼 今日工作任务较多，记得安排适当的休息时间');
    } else if (categoryDistribution.entertainment > 0.4) {
      recommendations.push('🎮 娱乐时间较多，可以考虑增加一些学习或工作任务');
    }

    return recommendations;
  }

  /**
   * 分析任务分类分布
   */
  private analyzeCategoryDistribution(schedule: DailySchedule): any {
    const total = schedule.estimatedDuration;
    if (total === 0) return { work: 0, improvement: 0, entertainment: 0 };

    const distribution = { work: 0, improvement: 0, entertainment: 0 };

    for (const slot of schedule.timeSlots) {
      const duration = (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60);
      distribution[slot.task.category] += duration;
    }

    return {
      work: distribution.work / total,
      improvement: distribution.improvement / total,
      entertainment: distribution.entertainment / total
    };
  }

  // ============================================================================
  // 算法性能监控
  // ============================================================================

  /**
   * 获取算法性能统计
   */
  getAlgorithmStats(): {
    planningCalls: number;
    balanceCalls: number;
    fixCalls: number;
    adjustmentCalls: number;
  } {
    // 这里可以添加性能监控逻辑
    return {
      planningCalls: 0,
      balanceCalls: 0,
      fixCalls: 0,
      adjustmentCalls: 0
    };
  }

  /**
   * 重置算法统计
   */
  resetAlgorithmStats(): void {
    // 重置性能统计
    console.log('算法统计已重置');
  }
}
