/**
 * 应用常量定义
 */

// ============================================================================
// 任务相关常量
// ============================================================================

export const TASK_CATEGORIES = {
  WORK: 'work',
  IMPROVEMENT: 'improvement',
  ENTERTAINMENT: 'entertainment'
} as const;

export const TASK_STATUSES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in-progress',
  COMPLETED: 'completed',
  POSTPONED: 'postponed'
} as const;

export const PRIORITY_LEVELS = {
  VERY_LOW: 1,
  LOW: 2,
  MEDIUM: 3,
  HIGH: 4,
  VERY_HIGH: 5
} as const;

export const QUADRANTS = {
  URGENT_IMPORTANT: 1,      // 重要且紧急
  IMPORTANT_NOT_URGENT: 2,  // 重要不紧急
  URGENT_NOT_IMPORTANT: 3,  // 紧急不重要
  NOT_URGENT_NOT_IMPORTANT: 4 // 不紧急不重要
} as const;

// ============================================================================
// 时间相关常量
// ============================================================================

export const TIME_CONSTANTS = {
  MINUTES_PER_HOUR: 60,
  HOURS_PER_DAY: 24,
  DAYS_PER_WEEK: 7,
  DEFAULT_BREAK_DURATION: 15, // 分钟
  DEFAULT_TASK_DURATION: 60,  // 分钟
  MIN_TASK_DURATION: 15,      // 分钟
  MAX_TASK_DURATION: 480      // 分钟 (8小时)
} as const;

export const WORK_DAYS = [
  'monday',
  'tuesday', 
  'wednesday',
  'thursday',
  'friday'
] as const;

export const ALL_DAYS = [
  'monday',
  'tuesday',
  'wednesday', 
  'thursday',
  'friday',
  'saturday',
  'sunday'
] as const;

export const DAY_NAMES_CN = {
  monday: '周一',
  tuesday: '周二',
  wednesday: '周三',
  thursday: '周四',
  friday: '周五',
  saturday: '周六',
  sunday: '周日'
} as const;

// ============================================================================
// 默认配置
// ============================================================================

export const DEFAULT_USER_CONFIG = {
  workStart: '09:00',
  workEnd: '18:00',
  workDays: WORK_DAYS,
  sleepStart: '23:00',
  sleepEnd: '07:00',
  fixedSlots: [
    { start: '07:00', end: '08:00', type: 'personal', label: '晨间例行' },
    { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' },
    { start: '18:30', end: '19:30', type: 'meal', label: '晚餐时间' }
  ],
  commuteToWork: 30,
  commuteFromWork: 30,
  preferredWorkHours: 'morning' as const,
  maxContinuousWork: 120,
  breakInterval: 25,
  categoryPreferences: {
    work: { 
      preferredTimes: ['09:00-12:00', '14:00-18:00'], 
      maxDaily: 480 
    },
    improvement: { 
      preferredTimes: ['07:00-09:00', '19:00-21:00'], 
      maxDaily: 120 
    },
    entertainment: { 
      preferredTimes: ['20:00-22:00'], 
      maxDaily: 180 
    }
  }
} as const;

// ============================================================================
// 算法相关常量
// ============================================================================

export const ALGORITHM_CONSTANTS = {
  // 四象限权重
  QUADRANT_WEIGHTS: {
    [QUADRANTS.URGENT_IMPORTANT]: 1.0,
    [QUADRANTS.IMPORTANT_NOT_URGENT]: 0.8,
    [QUADRANTS.URGENT_NOT_IMPORTANT]: 0.6,
    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: 0.4
  },
  
  // 推迟惩罚系数
  POSTPONE_PENALTY: 0.1,
  
  // 截止日期权重
  DEADLINE_WEIGHT: 0.3,
  
  // 分类权重
  CATEGORY_WEIGHTS: {
    [TASK_CATEGORIES.WORK]: 1.0,
    [TASK_CATEGORIES.IMPROVEMENT]: 0.8,
    [TASK_CATEGORIES.ENTERTAINMENT]: 0.6
  }
} as const;

// ============================================================================
// UI 相关常量
// ============================================================================

export const UI_CONSTANTS = {
  // 颜色主题
  COLORS: {
    PRIMARY: '#4F46E5',
    SUCCESS: '#10B981',
    WARNING: '#F59E0B',
    ERROR: '#EF4444',
    INFO: '#3B82F6'
  },
  
  // 象限颜色
  QUADRANT_COLORS: {
    [QUADRANTS.URGENT_IMPORTANT]: {
      bg: 'bg-red-100',
      border: 'border-red-300',
      text: 'text-red-800',
      selectedBg: 'bg-red-200',
      selectedBorder: 'border-red-500'
    },
    [QUADRANTS.IMPORTANT_NOT_URGENT]: {
      bg: 'bg-blue-100',
      border: 'border-blue-300', 
      text: 'text-blue-800',
      selectedBg: 'bg-blue-200',
      selectedBorder: 'border-blue-500'
    },
    [QUADRANTS.URGENT_NOT_IMPORTANT]: {
      bg: 'bg-yellow-100',
      border: 'border-yellow-300',
      text: 'text-yellow-800', 
      selectedBg: 'bg-yellow-200',
      selectedBorder: 'border-yellow-500'
    },
    [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: {
      bg: 'bg-gray-100',
      border: 'border-gray-300',
      text: 'text-gray-800',
      selectedBg: 'bg-gray-200', 
      selectedBorder: 'border-gray-500'
    }
  },
  
  // 分页
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
} as const;

// ============================================================================
// 错误代码
// ============================================================================

export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // 任务相关错误
  TASK_NOT_FOUND: 'TASK_NOT_FOUND',
  TASK_CREATION_FAILED: 'TASK_CREATION_FAILED',
  TASK_UPDATE_FAILED: 'TASK_UPDATE_FAILED',
  
  // 用户配置错误
  USER_CONFIG_NOT_FOUND: 'USER_CONFIG_NOT_FOUND',
  USER_CONFIG_INVALID: 'USER_CONFIG_INVALID',
  
  // 算法错误
  ALGORITHM_EXECUTION_FAILED: 'ALGORITHM_EXECUTION_FAILED',
  SCHEDULE_GENERATION_FAILED: 'SCHEDULE_GENERATION_FAILED'
} as const;
