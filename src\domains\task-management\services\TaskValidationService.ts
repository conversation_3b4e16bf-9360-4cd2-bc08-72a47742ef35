/**
 * 任务验证服务
 * 负责任务数据的验证和业务规则检查
 */

import {
  Task,
  TaskCategory,
  Priority,
  TaskStatus,
  TASK_CATEGORIES,
  PRIORITY_LEVELS,
  TASK_STATUSES,
  isValidTaskCategory,
  isValidPriority,
  isValidTaskStatus
} from '@/shared';

import {
  CreateTaskRequest,
  UpdateTaskRequest,
  CompleteTaskRequest,
  PostponeTaskRequest
} from '../models/Task';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class TaskValidationService {

  /**
   * 验证任务创建请求
   */
  validateCreateRequest(request: CreateTaskRequest): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证必填字段
    if (!request.userId || request.userId.trim().length === 0) {
      errors.push('用户ID不能为空');
    }

    if (!request.title || request.title.trim().length === 0) {
      errors.push('任务标题不能为空');
    } else if (request.title.length > 100) {
      errors.push('任务标题不能超过100个字符');
    }

    // 验证分类
    if (!request.category || !isValidTaskCategory(request.category)) {
      errors.push('请选择有效的任务分类');
    }

    // 验证重要性
    if (!isValidPriority(request.importance)) {
      errors.push('重要性必须在1-5之间');
    }

    // 验证紧急性
    if (!isValidPriority(request.urgency)) {
      errors.push('紧急性必须在1-5之间');
    }

    // 验证截止时间
    if (!request.deadline) {
      errors.push('请设置截止时间');
    } else {
      const now = new Date();
      if (request.deadline < now) {
        errors.push('截止时间不能早于当前时间');
      }
      
      // 警告：截止时间过于遥远
      const oneYearLater = new Date();
      oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
      if (request.deadline > oneYearLater) {
        warnings.push('截止时间设置得较远，建议设置更近的时间以保持紧迫感');
      }
    }

    // 验证预估时长
    if (!request.estimatedDuration || request.estimatedDuration <= 0) {
      errors.push('预估时长必须大于0');
    } else if (request.estimatedDuration < 15) {
      warnings.push('预估时长少于15分钟，建议合并到其他任务中');
    } else if (request.estimatedDuration > 480) {
      warnings.push('预估时长超过8小时，建议分解为更小的任务');
    }

    // 验证描述长度
    if (request.description && request.description.length > 500) {
      errors.push('任务描述不能超过500个字符');
    }

    // 业务规则验证
    this.validateBusinessRules(request, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证任务更新请求
   */
  validateUpdateRequest(request: UpdateTaskRequest): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证ID
    if (!request.id || request.id.trim().length === 0) {
      errors.push('任务ID不能为空');
    }

    // 验证标题（如果提供）
    if (request.title !== undefined) {
      if (request.title.trim().length === 0) {
        errors.push('任务标题不能为空');
      } else if (request.title.length > 100) {
        errors.push('任务标题不能超过100个字符');
      }
    }

    // 验证分类（如果提供）
    if (request.category !== undefined && !isValidTaskCategory(request.category)) {
      errors.push('请选择有效的任务分类');
    }

    // 验证重要性（如果提供）
    if (request.importance !== undefined && !isValidPriority(request.importance)) {
      errors.push('重要性必须在1-5之间');
    }

    // 验证紧急性（如果提供）
    if (request.urgency !== undefined && !isValidPriority(request.urgency)) {
      errors.push('紧急性必须在1-5之间');
    }

    // 验证截止时间（如果提供）
    if (request.deadline !== undefined) {
      const now = new Date();
      if (request.deadline < now) {
        errors.push('截止时间不能早于当前时间');
      }
    }

    // 验证预估时长（如果提供）
    if (request.estimatedDuration !== undefined) {
      if (request.estimatedDuration <= 0) {
        errors.push('预估时长必须大于0');
      } else if (request.estimatedDuration > 480) {
        warnings.push('预估时长超过8小时，建议分解为更小的任务');
      }
    }

    // 验证状态（如果提供）
    if (request.status !== undefined && !isValidTaskStatus(request.status)) {
      errors.push('请选择有效的任务状态');
    }

    // 验证描述长度（如果提供）
    if (request.description !== undefined && request.description.length > 500) {
      errors.push('任务描述不能超过500个字符');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证任务完成请求
   */
  validateCompleteRequest(request: CompleteTaskRequest, task: Task): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证任务状态
    if (task.status === 'completed') {
      errors.push('任务已经完成');
    }

    // 验证实际时长
    if (request.actualDuration !== undefined) {
      if (request.actualDuration <= 0) {
        errors.push('实际时长必须大于0');
      } else {
        // 检查实际时长与预估时长的差异
        const estimatedDuration = task.estimatedDuration;
        const ratio = request.actualDuration / estimatedDuration;
        
        if (ratio > 2) {
          warnings.push('实际时长远超预估时长，建议调整未来类似任务的时间预估');
        } else if (ratio < 0.5) {
          warnings.push('实际时长远少于预估时长，建议调整未来类似任务的时间预估');
        }
      }
    }

    // 验证满意度评分
    if (request.satisfactionScore !== undefined) {
      if (request.satisfactionScore < 1 || request.satisfactionScore > 5) {
        errors.push('满意度评分必须在1-5之间');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证任务推迟请求
   */
  validatePostponeRequest(request: PostponeTaskRequest, task: Task): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证任务状态
    if (task.status === 'completed') {
      errors.push('已完成的任务不能推迟');
    }

    // 验证推迟次数
    if (task.postponeCount >= 3) {
      warnings.push('该任务已推迟多次，建议重新评估任务的必要性或分解任务');
    }

    // 验证新的截止时间
    if (request.newDeadline) {
      const now = new Date();
      if (request.newDeadline <= now) {
        errors.push('新的截止时间必须晚于当前时间');
      }
      
      if (request.newDeadline <= task.deadline) {
        errors.push('新的截止时间必须晚于原截止时间');
      }
    }

    // 验证推迟原因
    if (request.reason && request.reason.length > 200) {
      errors.push('推迟原因不能超过200个字符');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证业务规则
   */
  private validateBusinessRules(request: CreateTaskRequest, errors: string[], warnings: string[]): void {
    // 规则1：娱乐任务不应该设置过高的重要性和紧急性
    if (request.category === TASK_CATEGORIES.ENTERTAINMENT) {
      if (request.importance >= 4 && request.urgency >= 4) {
        warnings.push('娱乐任务通常不需要设置过高的重要性和紧急性');
      }
    }

    // 规则2：工作任务在非工作时间的警告
    if (request.category === TASK_CATEGORIES.WORK) {
      const deadlineHour = request.deadline.getHours();
      if (deadlineHour < 9 || deadlineHour > 18) {
        warnings.push('工作任务的截止时间设置在非工作时间，请确认是否合理');
      }
    }

    // 规则3：高重要性但低紧急性的任务应该有合理的时间安排
    if (request.importance >= 4 && request.urgency <= 2) {
      const timeToDeadline = request.deadline.getTime() - new Date().getTime();
      const daysToDeadline = timeToDeadline / (1000 * 60 * 60 * 24);
      
      if (daysToDeadline < 1) {
        warnings.push('重要但不紧急的任务截止时间过近，可能影响执行质量');
      }
    }

    // 规则4：短时间任务的合理性检查
    if (request.estimatedDuration < 30) {
      const timeToDeadline = request.deadline.getTime() - new Date().getTime();
      const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);
      
      if (hoursToDeadline > 24 && request.urgency >= 4) {
        warnings.push('短时间任务设置了较远的截止时间但标记为紧急，请检查设置是否合理');
      }
    }
  }

  /**
   * 验证任务状态转换的合法性
   */
  validateStatusTransition(currentStatus: TaskStatus, newStatus: TaskStatus): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 定义合法的状态转换
    const validTransitions: Record<TaskStatus, TaskStatus[]> = {
      'pending': ['in-progress', 'postponed', 'completed'],
      'in-progress': ['completed', 'postponed', 'pending'],
      'completed': [], // 已完成的任务不能转换到其他状态
      'postponed': ['pending', 'in-progress', 'completed']
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      errors.push(`不能从状态 "${currentStatus}" 转换到 "${newStatus}"`);
    }

    // 特殊情况的警告
    if (currentStatus === 'completed' && newStatus !== 'completed') {
      warnings.push('重新激活已完成的任务，请确认是否必要');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
