/**
 * @deprecated 请使用 src/shared 中的类型定义
 * 这个文件保留是为了向后兼容，新代码请直接从 shared 导入
 */

// 重新导出共享类型以保持向后兼容
export type {
  Task,
  TaskCategory,
  Priority,
  TaskStatus,
  Quadrant,
  TimeSlot,
  DailySchedule,
  ScoredTask,
  UserProfile,
  WorkHours,
  UserTimeConfig,
  FixedTimeSlot,
  CategoryPreferences,
  CategoryPreference,
  CategoryRatios,
  BalanceAnalysis,
  WeeklyStats,
  DailyStats,
  PostponedTaskAlert,
  AdjustmentResult,
  ApiResponse,
  PaginatedResponse,
  CalendarEvent,
  AppError
} from '../shared';

// 保留一些特殊的类型定义，这些在 shared 中可能有不同的实现
export interface TaskCompletion {
  id: string;
  taskId: string;
  completedAt: Date;
  actualDuration: number;
  satisfactionScore: 1 | 2 | 3 | 4 | 5;
}

export interface Recommendation {
  task: Task;
  recommendedSlot: TimeSlot;
  reason: string;
  confidence: number;
}
