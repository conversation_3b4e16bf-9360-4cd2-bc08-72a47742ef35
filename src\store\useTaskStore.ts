import { create } from 'zustand';
import { Task, DailySchedule, BalanceAnalysis, PostponedTaskAlert } from '@/shared';
import { supabase } from '@/lib/supabase';
import { getDefaultPlanningService } from '@/domains/intelligent-planning';
import { getDefaultTaskService, CreateTaskRequest, UpdateTaskRequest } from '@/domains/task-management';

interface TaskState {
  // State
  tasks: Task[];
  dailySchedule: DailySchedule | null;
  balanceAnalysis: BalanceAnalysis | null;
  postponedAlerts: PostponedTaskAlert[];
  loading: boolean;
  error: string | null;
  
  // Services
  planningService: ReturnType<typeof getDefaultPlanningService>;
  taskService: ReturnType<typeof getDefaultTaskService>;
  
  // Actions
  fetchTasks: (userId: string) => Promise<void>;
  createTask: (task: CreateTaskRequest) => Promise<void>;
  updateTask: (id: string, updates: Partial<UpdateTaskRequest>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  completeTask: (id: string, actualDuration?: number, satisfaction?: number) => Promise<void>;
  postponeTask: (id: string, reason?: string) => Promise<void>;
  startTask: (id: string) => Promise<void>;
  pauseTask: (id: string) => Promise<void>;
  
  // Algorithm actions
  generateDailySchedule: (userId: string) => Promise<void>;
  analyzeBalance: (userId: string) => Promise<void>;
  analyzePostponedTasks: (userId: string) => Promise<void>;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  // Initial state
  tasks: [],
  dailySchedule: null,
  balanceAnalysis: null,
  postponedAlerts: [],
  loading: false,
  error: null,
  
  // Service instances
  planningService: getDefaultPlanningService(),
  taskService: getDefaultTaskService(),
  
  // Fetch tasks
  fetchTasks: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { taskService } = get();
      const result = await taskService.getUserTasks(userId);

      if (result.success) {
        set({ tasks: result.data, loading: false });
      } else {
        set({
          error: result.error || 'Failed to fetch tasks',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Create task
  createTask: async (taskData) => {
    try {
      set({ loading: true, error: null });

      const { taskService } = get();
      const result = await taskService.createTask(taskData);

      if (result.success) {
        set(state => ({
          tasks: [result.data, ...state.tasks],
          loading: false
        }));
      } else {
        set({
          error: result.error || 'Failed to create task',
          loading: false
        });
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create task';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  // Update task
  updateTask: async (id: string, updates: Partial<UpdateTaskRequest>) => {
    try {
      set({ loading: true, error: null });

      const { taskService } = get();
      const result = await taskService.updateTask({ id, ...updates });

      if (result.success) {
        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === id ? result.data : task
          ),
          loading: false
        }));
      } else {
        set({
          error: result.error || 'Failed to update task',
          loading: false
        });
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update task';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  // Delete task
  deleteTask: async (id: string) => {
    try {
      set({ loading: true, error: null });

      const { taskService } = get();
      const result = await taskService.deleteTask(id);

      if (result.success) {
        set(state => ({
          tasks: state.tasks.filter(task => task.id !== id),
          loading: false
        }));
      } else {
        set({
          error: result.error || 'Failed to delete task',
          loading: false
        });
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  // Complete task
  completeTask: async (id: string, actualDuration?: number, satisfaction?: number) => {
    try {
      const { taskService, planningService } = get();
      const task = get().tasks.find(t => t.id === id);

      if (!task) throw new Error('Task not found');

      // Complete task using task service
      const result = await taskService.completeTask({
        id,
        actualDuration,
        satisfactionScore: satisfaction
      });

      if (result.success) {
        // Update local state
        set(state => ({
          tasks: state.tasks.map(t =>
            t.id === id ? result.data : t
          )
        }));

        // Update daily stats
        await planningService.updateActivityStats(
          task.userId,
          task.category,
          actualDuration || task.estimatedDuration
        );
      } else {
        throw new Error(result.error);
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';
      set({ error: errorMessage });
      throw error;
    }
  },
  
  // Postpone task
  postponeTask: async (id: string, reason?: string) => {
    try {
      const { taskService } = get();
      const result = await taskService.postponeTask({
        id,
        reason
      });

      if (result.success) {
        // Update local state
        set(state => ({
          tasks: state.tasks.map(t =>
            t.id === id ? result.data : t
          )
        }));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Start task
  startTask: async (id: string) => {
    try {
      const { taskService } = get();
      const result = await taskService.startTask(id);

      if (result.success) {
        // Update local state
        set(state => ({
          tasks: state.tasks.map(t =>
            t.id === id ? result.data : t
          )
        }));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start task';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Pause task
  pauseTask: async (id: string) => {
    try {
      const { taskService } = get();
      const result = await taskService.pauseTask(id);

      if (result.success) {
        // Update local state
        set(state => ({
          tasks: state.tasks.map(t =>
            t.id === id ? result.data : t
          )
        }));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to pause task';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Generate daily schedule
  generateDailySchedule: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { tasks, planningService } = get();
      const userTasks = tasks.filter(task => task.userId === userId);

      // 获取用户时间配置
      let userTimeConfig = null;
      try {
        const { data: userProfile } = await supabase
          .from('user_profiles')
          .select('time_config')
          .eq('id', userId)
          .single();

        userTimeConfig = userProfile?.time_config;
      } catch (error) {
        console.log('No user time config found, using defaults');
      }

      // 使用新的规划服务
      const result = await planningService.generateDailyPlan(userId, userTasks, userTimeConfig);

      if (result.success) {
        set({
          dailySchedule: result.data.schedule,
          loading: false
        });
      } else {
        set({
          error: result.error || 'Failed to generate schedule',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Analyze balance
  analyzeBalance: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { planningService } = get();
      const result = await planningService.getBalanceAnalysis(userId);

      if (result.success) {
        set({ balanceAnalysis: result.data, loading: false });
      } else {
        set({
          error: result.error || 'Failed to analyze balance',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Analyze postponed tasks
  analyzePostponedTasks: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { tasks, planningService } = get();
      const userTasks = tasks.filter(task => task.userId === userId);
      const result = await planningService.getPostponedTasksAnalysis(userTasks);

      if (result.success) {
        set({ postponedAlerts: result.data.alerts, loading: false });
      } else {
        set({
          error: result.error || 'Failed to analyze postponed tasks',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Utility actions
  setLoading: (loading: boolean) => set({ loading }),
  setError: (error: string | null) => set({ error }),
  clearError: () => set({ error: null })
}));
