import { Task, TimeSlot, DailySchedule } from '@/types';

export interface TimeConstraint {
  type: 'work' | 'sleep' | 'meal' | 'commute' | 'personal' | 'break';
  start: string;
  end: string;
  label: string;
  isFixed: boolean; // 是否可以调整
  priority: number; // 优先级 1-10，10最高
}

export interface AdjustmentStrategy {
  name: string;
  description: string;
  canUseRestTime: boolean; // 是否可以占用休息时间
  canAdjustMeals: boolean; // 是否可以调整用餐时间
  canExtendWorkHours: boolean; // 是否可以延长工作时间
  maxOvertime: number; // 最大加班时间（分钟）
}

export interface AdjustmentResult {
  success: boolean;
  adjustedSchedule: TimeSlot[];
  adjustments: AdjustmentAction[];
  warnings: string[];
  timeRecovered: number; // 回收的时间（分钟）
  impactScore: number; // 影响评分 0-100，越低越好
}

export interface AdjustmentAction {
  type: 'postpone' | 'compress' | 'move' | 'split' | 'useRestTime' | 'extendWorkHours';
  taskId: string;
  taskTitle: string;
  originalTime: { start: Date; end: Date };
  newTime: { start: Date; end: Date };
  reason: string;
  impact: string;
}

export class TimeAdjustmentAlgorithm {
  private constraints: TimeConstraint[] = [];
  private strategies: Record<string, AdjustmentStrategy> = {
    conservative: {
      name: '保守策略',
      description: '优先调整低优先级任务，尽量不影响休息',
      canUseRestTime: false,
      canAdjustMeals: false,
      canExtendWorkHours: false,
      maxOvertime: 0
    },
    balanced: {
      name: '平衡策略', 
      description: '适度调整，可以占用部分休息时间',
      canUseRestTime: true,
      canAdjustMeals: false,
      canExtendWorkHours: true,
      maxOvertime: 60
    },
    aggressive: {
      name: '激进策略',
      description: '为完成紧急任务，可以大幅调整时间安排',
      canUseRestTime: true,
      canAdjustMeals: true,
      canExtendWorkHours: true,
      maxOvertime: 120
    }
  };

  /**
   * 设置用户的时间约束
   */
  setTimeConstraints(userConfig: any) {
    this.constraints = [
      // 工作时间
      {
        type: 'work',
        start: userConfig.workStart,
        end: userConfig.workEnd,
        label: '工作时间',
        isFixed: false,
        priority: 8
      },
      // 睡眠时间
      {
        type: 'sleep',
        start: userConfig.sleepStart,
        end: userConfig.sleepEnd,
        label: '睡眠时间',
        isFixed: true,
        priority: 10
      },
      // 固定时间段
      ...userConfig.fixedSlots.map((slot: any) => ({
        type: slot.type,
        start: slot.start,
        end: slot.end,
        label: slot.label,
        isFixed: slot.type === 'meal',
        priority: slot.type === 'meal' ? 9 : 7
      }))
    ];
  }

  /**
   * 处理任务超时的智能调整
   */
  async handleTaskOverrun(
    currentSchedule: TimeSlot[],
    overrunTask: Task,
    actualDuration: number,
    strategyName: string = 'balanced'
  ): Promise<AdjustmentResult> {
    const strategy = this.strategies[strategyName];
    const overrunMinutes = actualDuration - overrunTask.estimatedDuration;
    
    console.log(`任务 "${overrunTask.title}" 超时 ${overrunMinutes} 分钟，使用 ${strategy.name} 进行调整`);

    // 找到受影响的后续任务
    const overrunTaskSlot = currentSchedule.find(slot => slot.task.id === overrunTask.id);
    if (!overrunTaskSlot) {
      return this.createFailureResult('未找到超时任务');
    }

    const affectedSlots = currentSchedule.filter(slot => 
      slot.startTime >= overrunTaskSlot.endTime
    ).sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

    return this.adjustScheduleForOverrun(
      currentSchedule,
      overrunTaskSlot,
      overrunMinutes,
      affectedSlots,
      strategy
    );
  }

  /**
   * 处理新增紧急任务
   */
  async handleUrgentTask(
    currentSchedule: TimeSlot[],
    urgentTask: Task,
    preferredTime: Date,
    strategyName: string = 'balanced'
  ): Promise<AdjustmentResult> {
    const strategy = this.strategies[strategyName];
    
    console.log(`新增紧急任务 "${urgentTask.title}"，需要 ${urgentTask.estimatedDuration} 分钟`);

    // 寻找可用时间段
    const availableSlots = this.findAvailableTimeSlots(currentSchedule, urgentTask.estimatedDuration);
    
    if (availableSlots.length > 0) {
      // 有空闲时间，直接插入
      return this.insertTaskInAvailableSlot(currentSchedule, urgentTask, availableSlots[0]);
    }

    // 没有空闲时间，需要调整现有安排
    return this.makeSpaceForUrgentTask(currentSchedule, urgentTask, preferredTime, strategy);
  }

  /**
   * 处理任务超时的调整逻辑
   */
  private adjustScheduleForOverrun(
    schedule: TimeSlot[],
    overrunSlot: TimeSlot,
    overrunMinutes: number,
    affectedSlots: TimeSlot[],
    strategy: AdjustmentStrategy
  ): AdjustmentResult {
    const adjustments: AdjustmentAction[] = [];
    const warnings: string[] = [];
    let timeRecovered = 0;
    let impactScore = 0;

    // 更新超时任务的结束时间
    const newOverrunEnd = new Date(overrunSlot.endTime.getTime() + overrunMinutes * 60000);
    
    adjustments.push({
      type: 'compress',
      taskId: overrunSlot.task.id,
      taskTitle: overrunSlot.task.title,
      originalTime: { start: overrunSlot.startTime, end: overrunSlot.endTime },
      newTime: { start: overrunSlot.startTime, end: newOverrunEnd },
      reason: `任务实际用时超出预估 ${overrunMinutes} 分钟`,
      impact: '延长了任务时间'
    });

    let currentDelay = overrunMinutes;
    const adjustedSlots = [...schedule];

    // 逐个调整后续任务
    for (const slot of affectedSlots) {
      if (currentDelay <= 0) break;

      const adjustmentResult = this.adjustSingleTask(slot, currentDelay, strategy);
      
      if (adjustmentResult.success) {
        adjustments.push(...adjustmentResult.adjustments);
        timeRecovered += adjustmentResult.timeRecovered;
        currentDelay -= adjustmentResult.timeRecovered;
        impactScore += adjustmentResult.impactScore;
        
        // 更新时间段
        const slotIndex = adjustedSlots.findIndex(s => s.task.id === slot.task.id);
        if (slotIndex !== -1) {
          adjustedSlots[slotIndex] = {
            ...slot,
            startTime: new Date(slot.startTime.getTime() + (overrunMinutes - currentDelay) * 60000),
            endTime: new Date(slot.endTime.getTime() + (overrunMinutes - currentDelay) * 60000)
          };
        }
      } else {
        warnings.push(`无法调整任务 "${slot.task.title}": ${adjustmentResult.warnings.join(', ')}`);
      }
    }

    // 如果还有剩余延迟，尝试其他策略
    if (currentDelay > 0) {
      const additionalResult = this.handleRemainingDelay(currentDelay, strategy);
      adjustments.push(...additionalResult.adjustments);
      warnings.push(...additionalResult.warnings);
      impactScore += additionalResult.impactScore;
    }

    return {
      success: currentDelay <= 0,
      adjustedSchedule: adjustedSlots,
      adjustments,
      warnings,
      timeRecovered,
      impactScore
    };
  }

  /**
   * 调整单个任务
   */
  private adjustSingleTask(
    slot: TimeSlot,
    neededTime: number,
    strategy: AdjustmentStrategy
  ): AdjustmentResult {
    const task = slot.task;
    const adjustments: AdjustmentAction[] = [];
    const warnings: string[] = [];
    let timeRecovered = 0;
    let impactScore = 0;

    // 根据任务优先级和策略决定调整方式
    if (task.urgency <= 2 && task.importance <= 2) {
      // 低优先级任务：可以推迟
      adjustments.push({
        type: 'postpone',
        taskId: task.id,
        taskTitle: task.title,
        originalTime: { start: slot.startTime, end: slot.endTime },
        newTime: { start: new Date(slot.startTime.getTime() + neededTime * 60000), end: new Date(slot.endTime.getTime() + neededTime * 60000) },
        reason: '为紧急任务让出时间',
        impact: '任务被推迟'
      });
      timeRecovered = Math.min(neededTime, task.estimatedDuration);
      impactScore = 20;
    } else if (task.category === 'entertainment' && strategy.canUseRestTime) {
      // 娱乐时间：可以压缩或取消
      const compressionRatio = 0.5; // 压缩50%
      const compressedDuration = Math.max(15, task.estimatedDuration * compressionRatio);
      const savedTime = task.estimatedDuration - compressedDuration;
      
      adjustments.push({
        type: 'compress',
        taskId: task.id,
        taskTitle: task.title,
        originalTime: { start: slot.startTime, end: slot.endTime },
        newTime: { start: slot.startTime, end: new Date(slot.startTime.getTime() + compressedDuration * 60000) },
        reason: '压缩娱乐时间以应对紧急情况',
        impact: `娱乐时间减少 ${savedTime} 分钟`
      });
      timeRecovered = Math.min(neededTime, savedTime);
      impactScore = 30;
    } else if (task.category === 'improvement' && neededTime > 30) {
      // 提升类任务：可以分割
      const splitPoint = Math.floor(task.estimatedDuration / 2);
      adjustments.push({
        type: 'split',
        taskId: task.id,
        taskTitle: task.title,
        originalTime: { start: slot.startTime, end: slot.endTime },
        newTime: { start: slot.startTime, end: new Date(slot.startTime.getTime() + splitPoint * 60000) },
        reason: '将学习任务分割，为紧急任务腾出时间',
        impact: '任务被分为两部分'
      });
      timeRecovered = Math.min(neededTime, task.estimatedDuration - splitPoint);
      impactScore = 40;
    } else {
      // 其他情况：只能推迟
      warnings.push(`任务 "${task.title}" 优先级较高，建议手动调整`);
      impactScore = 60;
    }

    return {
      success: timeRecovered > 0,
      adjustedSchedule: [],
      adjustments,
      warnings,
      timeRecovered,
      impactScore
    };
  }

  /**
   * 处理剩余延迟
   */
  private handleRemainingDelay(remainingDelay: number, strategy: AdjustmentStrategy): AdjustmentResult {
    const adjustments: AdjustmentAction[] = [];
    const warnings: string[] = [];
    let impactScore = 0;

    if (strategy.canExtendWorkHours && remainingDelay <= strategy.maxOvertime) {
      adjustments.push({
        type: 'extendWorkHours',
        taskId: 'system',
        taskTitle: '延长工作时间',
        originalTime: { start: new Date(), end: new Date() },
        newTime: { start: new Date(), end: new Date() },
        reason: `延长工作时间 ${remainingDelay} 分钟以完成所有任务`,
        impact: '今日工作时间延长'
      });
      impactScore = 50;
    } else if (strategy.canUseRestTime) {
      adjustments.push({
        type: 'useRestTime',
        taskId: 'system',
        taskTitle: '占用休息时间',
        originalTime: { start: new Date(), end: new Date() },
        newTime: { start: new Date(), end: new Date() },
        reason: `占用 ${remainingDelay} 分钟休息时间`,
        impact: '休息时间减少'
      });
      impactScore = 70;
    } else {
      warnings.push(`无法完全解决 ${remainingDelay} 分钟的时间冲突，建议手动调整或推迟部分任务到明天`);
      impactScore = 90;
    }

    return {
      success: adjustments.length > 0,
      adjustedSchedule: [],
      adjustments,
      warnings,
      timeRecovered: remainingDelay,
      impactScore
    };
  }

  /**
   * 寻找可用时间段
   */
  private findAvailableTimeSlots(schedule: TimeSlot[], duration: number): { start: Date; end: Date }[] {
    const availableSlots: { start: Date; end: Date }[] = [];
    const sortedSlots = [...schedule].sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

    // 检查时间段之间的空隙
    for (let i = 0; i < sortedSlots.length - 1; i++) {
      const currentEnd = sortedSlots[i].endTime;
      const nextStart = sortedSlots[i + 1].startTime;
      const gapMinutes = (nextStart.getTime() - currentEnd.getTime()) / 60000;

      if (gapMinutes >= duration) {
        availableSlots.push({
          start: currentEnd,
          end: new Date(currentEnd.getTime() + duration * 60000)
        });
      }
    }

    return availableSlots;
  }

  /**
   * 在可用时间段插入任务
   */
  private insertTaskInAvailableSlot(
    schedule: TimeSlot[],
    task: Task,
    timeSlot: { start: Date; end: Date }
  ): AdjustmentResult {
    const newSlot: TimeSlot = {
      task,
      startTime: timeSlot.start,
      endTime: timeSlot.end,
      isFixed: false
    };

    return {
      success: true,
      adjustedSchedule: [...schedule, newSlot],
      adjustments: [{
        type: 'move',
        taskId: task.id,
        taskTitle: task.title,
        originalTime: { start: new Date(), end: new Date() },
        newTime: { start: timeSlot.start, end: timeSlot.end },
        reason: '在空闲时间段安排紧急任务',
        impact: '无影响'
      }],
      warnings: [],
      timeRecovered: task.estimatedDuration,
      impactScore: 0
    };
  }

  /**
   * 为紧急任务腾出空间
   */
  private makeSpaceForUrgentTask(
    schedule: TimeSlot[],
    urgentTask: Task,
    preferredTime: Date,
    strategy: AdjustmentStrategy
  ): AdjustmentResult {
    // 找到最接近首选时间的低优先级任务
    const candidateSlots = schedule
      .filter(slot => {
        const task = slot.task;
        return task.urgency <= 3 || task.importance <= 3 || 
               (task.category === 'entertainment' && strategy.canUseRestTime);
      })
      .sort((a, b) => {
        const aDistance = Math.abs(a.startTime.getTime() - preferredTime.getTime());
        const bDistance = Math.abs(b.startTime.getTime() - preferredTime.getTime());
        return aDistance - bDistance;
      });

    if (candidateSlots.length === 0) {
      return this.createFailureResult('没有可调整的任务时间段');
    }

    // 调整第一个候选时间段
    const targetSlot = candidateSlots[0];
    return this.adjustSingleTask(targetSlot, urgentTask.estimatedDuration, strategy);
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(reason: string): AdjustmentResult {
    return {
      success: false,
      adjustedSchedule: [],
      adjustments: [],
      warnings: [reason],
      timeRecovered: 0,
      impactScore: 100
    };
  }

  /**
   * 获取可用的调整策略
   */
  getAvailableStrategies(): AdjustmentStrategy[] {
    return Object.values(this.strategies);
  }

  /**
   * 评估调整的影响
   */
  evaluateAdjustmentImpact(result: AdjustmentResult): string {
    if (result.impactScore <= 20) {
      return '影响很小，建议执行';
    } else if (result.impactScore <= 40) {
      return '影响适中，可以接受';
    } else if (result.impactScore <= 60) {
      return '影响较大，需要谨慎考虑';
    } else {
      return '影响很大，建议寻找其他解决方案';
    }
  }
}
