import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@supabase/supabase-js';
import { auth } from '@/lib/supabase';

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      loading: false,
      error: null,
      
      signIn: async (email: string, password: string) => {
        try {
          set({ loading: true, error: null });
          
          const { data, error } = await auth.signIn(email, password);
          
          if (error) {
            throw new Error(error.message);
          }
          
          if (data.user) {
            set({ user: data.user, loading: false });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },
      
      signUp: async (email: string, password: string) => {
        try {
          set({ loading: true, error: null });
          
          const { data, error } = await auth.signUp(email, password);
          
          if (error) {
            throw new Error(error.message);
          }
          
          if (data.user) {
            set({ user: data.user, loading: false });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },
      
      signOut: async () => {
        try {
          set({ loading: true, error: null });
          
          const { error } = await auth.signOut();
          
          if (error) {
            throw new Error(error.message);
          }
          
          set({ user: null, loading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },
      
      setUser: (user: User | null) => {
        set({ user });
      },
      
      setLoading: (loading: boolean) => {
        set({ loading });
      },
      
      setError: (error: string | null) => {
        set({ error });
      },
      
      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'timemanager-auth',
      partialize: (state) => ({
        user: state.user
      }),
      // 使用 localStorage 持久化
      storage: {
        getItem: (name) => {
          if (typeof window !== 'undefined') {
            const value = localStorage.getItem(name);
            return value ? JSON.parse(value) : null;
          }
          return null;
        },
        setItem: (name, value) => {
          if (typeof window !== 'undefined') {
            localStorage.setItem(name, JSON.stringify(value));
          }
        },
        removeItem: (name) => {
          if (typeof window !== 'undefined') {
            localStorage.removeItem(name);
          }
        },
      },
    }
  )
);
