/**
 * 时间调整算法
 * 当任务超时时，动态调整后续任务的时间安排
 */

import {
  Task,
  TimeSlot,
  AdjustmentResult,
  UserTimeConfig,
  Priority,
  classifyQuadrant,
  isTimeRangeOverlap,
  getMinutesBetween,
  QUADRANTS
} from '@/shared';

export class TimeAdjustmentAlgorithm {

  /**
   * 当任务超时时调整后续安排
   */
  adjustForOverrun(
    overrunTask: Task,
    actualDuration: number,
    currentSchedule: TimeSlot[],
    userTimeConfig?: UserTimeConfig
  ): AdjustmentResult {
    const originalDuration = overrunTask.estimatedDuration;
    const overrunMinutes = actualDuration - originalDuration;

    if (overrunMinutes <= 0) {
      return {
        success: true,
        adjustedSchedule: currentSchedule,
        affectedTasks: [],
        message: '任务按时完成，无需调整',
        impactScore: 0
      };
    }

    // 找到超时任务在当前安排中的位置
    const overrunSlotIndex = currentSchedule.findIndex(slot => slot.task.id === overrunTask.id);
    
    if (overrunSlotIndex === -1) {
      return {
        success: false,
        adjustedSchedule: currentSchedule,
        affectedTasks: [],
        message: '未找到超时任务的时间安排',
        impactScore: 0
      };
    }

    // 计算调整策略
    const adjustmentStrategy = this.calculateAdjustmentStrategy(
      overrunMinutes,
      currentSchedule,
      overrunSlotIndex,
      userTimeConfig
    );

    return this.applyAdjustmentStrategy(
      adjustmentStrategy,
      currentSchedule,
      overrunSlotIndex,
      overrunMinutes
    );
  }

  /**
   * 计算调整策略
   */
  private calculateAdjustmentStrategy(
    overrunMinutes: number,
    schedule: TimeSlot[],
    overrunIndex: number,
    userTimeConfig?: UserTimeConfig
  ): 'compress' | 'postpone' | 'reschedule' | 'hybrid' {
    const remainingSlots = schedule.slice(overrunIndex + 1);
    
    if (remainingSlots.length === 0) {
      return 'postpone';
    }

    // 计算可压缩的时间
    const compressibleTime = this.calculateCompressibleTime(remainingSlots);
    
    if (compressibleTime >= overrunMinutes) {
      return 'compress';
    } else if (compressibleTime >= overrunMinutes * 0.6) {
      return 'hybrid'; // 部分压缩，部分推迟
    } else {
      return 'reschedule';
    }
  }

  /**
   * 计算可压缩的时间
   */
  private calculateCompressibleTime(slots: TimeSlot[]): number {
    let compressibleTime = 0;

    for (const slot of slots) {
      const task = slot.task;
      const quadrant = classifyQuadrant(task.importance, task.urgency);
      const duration = getMinutesBetween(slot.startTime, slot.endTime);

      // 根据任务重要性确定可压缩比例
      let compressRatio = 0;
      switch (quadrant) {
        case QUADRANTS.URGENT_IMPORTANT:
          compressRatio = 0.1; // 最多压缩10%
          break;
        case QUADRANTS.IMPORTANT_NOT_URGENT:
          compressRatio = 0.2; // 最多压缩20%
          break;
        case QUADRANTS.URGENT_NOT_IMPORTANT:
          compressRatio = 0.3; // 最多压缩30%
          break;
        case QUADRANTS.NOT_URGENT_NOT_IMPORTANT:
          compressRatio = 0.5; // 最多压缩50%
          break;
      }

      compressibleTime += duration * compressRatio;
    }

    return Math.floor(compressibleTime);
  }

  /**
   * 应用调整策略
   */
  private applyAdjustmentStrategy(
    strategy: string,
    schedule: TimeSlot[],
    overrunIndex: number,
    overrunMinutes: number
  ): AdjustmentResult {
    const adjustedSchedule = [...schedule];
    const affectedTasks: Task[] = [];
    let impactScore = 0;

    switch (strategy) {
      case 'compress':
        return this.applyCompressionStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
      
      case 'postpone':
        return this.applyPostponeStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
      
      case 'reschedule':
        return this.applyRescheduleStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
      
      case 'hybrid':
        return this.applyHybridStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
      
      default:
        return {
          success: false,
          adjustedSchedule: schedule,
          affectedTasks: [],
          message: '未知的调整策略',
          impactScore: 0
        };
    }
  }

  /**
   * 应用压缩策略
   */
  private applyCompressionStrategy(
    schedule: TimeSlot[],
    overrunIndex: number,
    overrunMinutes: number
  ): AdjustmentResult {
    const affectedTasks: Task[] = [];
    let remainingOverrun = overrunMinutes;
    let impactScore = 0;

    // 延长超时任务的结束时间
    schedule[overrunIndex].endTime = new Date(
      schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000
    );

    // 压缩后续任务
    for (let i = overrunIndex + 1; i < schedule.length && remainingOverrun > 0; i++) {
      const slot = schedule[i];
      const task = slot.task;
      const quadrant = classifyQuadrant(task.importance, task.urgency);
      const duration = getMinutesBetween(slot.startTime, slot.endTime);

      // 计算压缩比例
      let compressRatio = 0;
      switch (quadrant) {
        case QUADRANTS.URGENT_IMPORTANT:
          compressRatio = 0.1;
          impactScore += 10;
          break;
        case QUADRANTS.IMPORTANT_NOT_URGENT:
          compressRatio = 0.2;
          impactScore += 6;
          break;
        case QUADRANTS.URGENT_NOT_IMPORTANT:
          compressRatio = 0.3;
          impactScore += 4;
          break;
        case QUADRANTS.NOT_URGENT_NOT_IMPORTANT:
          compressRatio = 0.5;
          impactScore += 2;
          break;
      }

      const maxCompress = Math.floor(duration * compressRatio);
      const actualCompress = Math.min(maxCompress, remainingOverrun);

      if (actualCompress > 0) {
        // 调整开始时间（向后推迟）
        slot.startTime = new Date(slot.startTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000);
        
        // 调整结束时间（压缩持续时间）
        slot.endTime = new Date(slot.endTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000 - actualCompress * 60 * 1000);
        
        affectedTasks.push(task);
        remainingOverrun -= actualCompress;
      } else {
        // 只是向后推迟，不压缩
        slot.startTime = new Date(slot.startTime.getTime() + remainingOverrun * 60 * 1000);
        slot.endTime = new Date(slot.endTime.getTime() + remainingOverrun * 60 * 1000);
      }
    }

    return {
      success: remainingOverrun === 0,
      adjustedSchedule: schedule,
      affectedTasks,
      message: remainingOverrun === 0 
        ? `成功通过压缩后续任务调整了${overrunMinutes}分钟的超时`
        : `部分调整成功，仍有${remainingOverrun}分钟需要其他处理`,
      impactScore
    };
  }

  /**
   * 应用推迟策略
   */
  private applyPostponeStrategy(
    schedule: TimeSlot[],
    overrunIndex: number,
    overrunMinutes: number
  ): AdjustmentResult {
    // 延长超时任务的结束时间
    schedule[overrunIndex].endTime = new Date(
      schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000
    );

    // 将后续所有任务向后推迟
    for (let i = overrunIndex + 1; i < schedule.length; i++) {
      schedule[i].startTime = new Date(schedule[i].startTime.getTime() + overrunMinutes * 60 * 1000);
      schedule[i].endTime = new Date(schedule[i].endTime.getTime() + overrunMinutes * 60 * 1000);
    }

    const affectedTasks = schedule.slice(overrunIndex + 1).map(slot => slot.task);

    return {
      success: true,
      adjustedSchedule: schedule,
      affectedTasks,
      message: `所有后续任务向后推迟${overrunMinutes}分钟`,
      impactScore: affectedTasks.length * 3 // 推迟的影响相对较小
    };
  }

  /**
   * 应用重新安排策略
   */
  private applyRescheduleStrategy(
    schedule: TimeSlot[],
    overrunIndex: number,
    overrunMinutes: number
  ): AdjustmentResult {
    const affectedTasks: Task[] = [];
    const remainingSlots = schedule.slice(overrunIndex + 1);
    
    // 延长超时任务
    schedule[overrunIndex].endTime = new Date(
      schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000
    );

    // 选择低优先级任务移到明天
    const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);
    
    // 移除被重新安排的任务
    const newSchedule = schedule.filter(slot => 
      !tasksToReschedule.some(task => task.id === slot.task.id)
    );

    // 调整剩余任务的时间
    let timeOffset = overrunMinutes;
    for (let i = overrunIndex + 1; i < newSchedule.length; i++) {
      newSchedule[i].startTime = new Date(newSchedule[i].startTime.getTime() + timeOffset * 60 * 1000);
      newSchedule[i].endTime = new Date(newSchedule[i].endTime.getTime() + timeOffset * 60 * 1000);
    }

    return {
      success: true,
      adjustedSchedule: newSchedule,
      affectedTasks: tasksToReschedule,
      message: `${tasksToReschedule.length}个低优先级任务被重新安排到明天`,
      impactScore: tasksToReschedule.length * 8 // 重新安排的影响较大
    };
  }

  /**
   * 应用混合策略
   */
  private applyHybridStrategy(
    schedule: TimeSlot[],
    overrunIndex: number,
    overrunMinutes: number
  ): AdjustmentResult {
    // 先尝试压缩
    const compressResult = this.applyCompressionStrategy([...schedule], overrunIndex, overrunMinutes);
    
    if (compressResult.success) {
      return compressResult;
    }

    // 如果压缩不够，再结合重新安排
    const remainingOverrun = overrunMinutes - this.calculateCompressibleTime(schedule.slice(overrunIndex + 1));
    const rescheduleResult = this.applyRescheduleStrategy([...schedule], overrunIndex, remainingOverrun);

    return {
      success: true,
      adjustedSchedule: rescheduleResult.adjustedSchedule,
      affectedTasks: [...compressResult.affectedTasks, ...rescheduleResult.affectedTasks],
      message: `采用混合策略：压缩部分任务，重新安排${rescheduleResult.affectedTasks.length}个任务`,
      impactScore: compressResult.impactScore + rescheduleResult.impactScore
    };
  }

  /**
   * 选择需要重新安排的任务
   */
  private selectTasksToReschedule(slots: TimeSlot[], targetMinutes: number): Task[] {
    // 按优先级排序，选择低优先级任务
    const sortedSlots = [...slots].sort((a, b) => {
      const aQuadrant = classifyQuadrant(a.task.importance, a.task.urgency);
      const bQuadrant = classifyQuadrant(b.task.importance, b.task.urgency);
      return bQuadrant - aQuadrant; // 降序，低优先级在前
    });

    const tasksToReschedule: Task[] = [];
    let freedTime = 0;

    for (const slot of sortedSlots) {
      if (freedTime >= targetMinutes) break;
      
      const duration = getMinutesBetween(slot.startTime, slot.endTime);
      tasksToReschedule.push(slot.task);
      freedTime += duration;
    }

    return tasksToReschedule;
  }

  /**
   * 预测调整的影响
   */
  predictAdjustmentImpact(
    overrunMinutes: number,
    schedule: TimeSlot[],
    overrunIndex: number
  ): {
    strategy: string;
    affectedTasksCount: number;
    impactScore: number;
    description: string;
  } {
    const remainingSlots = schedule.slice(overrunIndex + 1);
    const compressibleTime = this.calculateCompressibleTime(remainingSlots);

    if (compressibleTime >= overrunMinutes) {
      return {
        strategy: 'compress',
        affectedTasksCount: remainingSlots.length,
        impactScore: remainingSlots.length * 3,
        description: '通过压缩后续任务时间来调整'
      };
    } else if (remainingSlots.length === 0) {
      return {
        strategy: 'postpone',
        affectedTasksCount: 0,
        impactScore: 0,
        description: '无后续任务，无需调整'
      };
    } else {
      const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);
      return {
        strategy: 'reschedule',
        affectedTasksCount: tasksToReschedule.length,
        impactScore: tasksToReschedule.length * 8,
        description: `${tasksToReschedule.length}个任务将被重新安排`
      };
    }
  }
}
