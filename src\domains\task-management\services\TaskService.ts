/**
 * 任务服务
 * 提供任务管理的核心业务逻辑
 */

import {
  Task,
  TaskCategory,
  TaskStatus,
  ApiResponse,
  createAppError,
  ERROR_CODES,
  classifyQuadrant,
  sortTasksByPriority,
  sortTasksByDeadline,
  filterTasksByCategory,
  filterTasksByStatus,
  isTaskOverdue,
  isTaskDueSoon
} from '@/shared';

import {
  CreateTaskRequest,
  UpdateTaskRequest,
  CompleteTaskRequest,
  PostponeTaskRequest,
  TaskFilter,
  TaskSortConfig,
  TaskPaginationOptions,
  TaskPaginationResult,
  TaskStatistics
} from '../models/Task';

import { TaskRepository } from '../repositories/TaskRepository';
import { TaskValidationService } from './TaskValidationService';

export class TaskService {
  private repository: TaskRepository;
  private validationService: TaskValidationService;

  constructor() {
    this.repository = new TaskRepository();
    this.validationService = new TaskValidationService();
  }

  // ============================================================================
  // 基础 CRUD 操作
  // ============================================================================

  /**
   * 获取用户的所有任务
   */
  async getUserTasks(userId: string): Promise<ApiResponse<Task[]>> {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: []
      };
    }

    return await this.repository.findByUserId(userId);
  }

  /**
   * 根据ID获取任务
   */
  async getTaskById(id: string): Promise<ApiResponse<Task | null>> {
    if (!id) {
      return {
        success: false,
        error: '任务ID不能为空',
        data: null
      };
    }

    return await this.repository.findById(id);
  }

  /**
   * 创建新任务
   */
  async createTask(request: CreateTaskRequest): Promise<ApiResponse<Task>> {
    // 验证请求数据
    const validation = this.validationService.validateCreateRequest(request);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        data: null as any
      };
    }

    // 创建任务
    const result = await this.repository.create(request);
    
    if (result.success && validation.warnings.length > 0) {
      console.warn('任务创建警告:', validation.warnings);
    }

    return result;
  }

  /**
   * 更新任务
   */
  async updateTask(request: UpdateTaskRequest): Promise<ApiResponse<Task>> {
    // 验证请求数据
    const validation = this.validationService.validateUpdateRequest(request);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        data: null as any
      };
    }

    // 如果更新状态，验证状态转换
    if (request.status) {
      const currentTaskResult = await this.repository.findById(request.id);
      if (!currentTaskResult.success || !currentTaskResult.data) {
        return {
          success: false,
          error: '任务不存在',
          data: null as any
        };
      }

      const statusValidation = this.validationService.validateStatusTransition(
        currentTaskResult.data.status,
        request.status
      );
      
      if (!statusValidation.isValid) {
        return {
          success: false,
          error: statusValidation.errors.join('; '),
          data: null as any
        };
      }
    }

    return await this.repository.update(request);
  }

  /**
   * 删除任务
   */
  async deleteTask(id: string): Promise<ApiResponse<void>> {
    if (!id) {
      return {
        success: false,
        error: '任务ID不能为空',
        data: undefined
      };
    }

    // 检查任务是否存在
    const taskResult = await this.repository.findById(id);
    if (!taskResult.success || !taskResult.data) {
      return {
        success: false,
        error: '任务不存在',
        data: undefined
      };
    }

    return await this.repository.delete(id);
  }

  // ============================================================================
  // 任务状态管理
  // ============================================================================

  /**
   * 完成任务
   */
  async completeTask(request: CompleteTaskRequest): Promise<ApiResponse<Task>> {
    // 获取当前任务
    const taskResult = await this.repository.findById(request.id);
    if (!taskResult.success || !taskResult.data) {
      return {
        success: false,
        error: '任务不存在',
        data: null as any
      };
    }

    const task = taskResult.data;

    // 验证完成请求
    const validation = this.validationService.validateCompleteRequest(request, task);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        data: null as any
      };
    }

    // 更新任务状态
    const updateResult = await this.repository.update({
      id: request.id,
      status: 'completed'
    });

    if (updateResult.success && validation.warnings.length > 0) {
      console.warn('任务完成警告:', validation.warnings);
    }

    return updateResult;
  }

  /**
   * 推迟任务
   */
  async postponeTask(request: PostponeTaskRequest): Promise<ApiResponse<Task>> {
    // 获取当前任务
    const taskResult = await this.repository.findById(request.id);
    if (!taskResult.success || !taskResult.data) {
      return {
        success: false,
        error: '任务不存在',
        data: null as any
      };
    }

    const task = taskResult.data;

    // 验证推迟请求
    const validation = this.validationService.validatePostponeRequest(request, task);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        data: null as any
      };
    }

    // 更新任务
    const updateData: UpdateTaskRequest = {
      id: request.id,
      status: 'postponed',
      postponeCount: task.postponeCount + 1
    };

    if (request.newDeadline) {
      updateData.deadline = request.newDeadline;
    }

    const updateResult = await this.repository.update(updateData);

    if (updateResult.success && validation.warnings.length > 0) {
      console.warn('任务推迟警告:', validation.warnings);
    }

    return updateResult;
  }

  /**
   * 开始任务
   */
  async startTask(id: string): Promise<ApiResponse<Task>> {
    return await this.updateTask({
      id,
      status: 'in-progress'
    });
  }

  /**
   * 暂停任务
   */
  async pauseTask(id: string): Promise<ApiResponse<Task>> {
    return await this.updateTask({
      id,
      status: 'pending'
    });
  }

  // ============================================================================
  // 任务查询和过滤
  // ============================================================================

  /**
   * 根据条件过滤任务
   */
  async getTasksByFilter(filter: TaskFilter): Promise<ApiResponse<Task[]>> {
    return await this.repository.findByFilter(filter);
  }

  /**
   * 分页获取任务
   */
  async getTasksWithPagination(
    userId: string,
    pagination: TaskPaginationOptions,
    sort?: TaskSortConfig
  ): Promise<ApiResponse<TaskPaginationResult>> {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: null as any
      };
    }

    return await this.repository.findWithPagination(userId, pagination, sort);
  }

  /**
   * 获取今日任务
   */
  async getTodayTasks(userId: string): Promise<ApiResponse<Task[]>> {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const filter: TaskFilter = {
      userId,
      status: ['pending', 'in-progress'],
      endDate: tomorrow
    };

    return await this.getTasksByFilter(filter);
  }

  /**
   * 获取过期任务
   */
  async getOverdueTasks(userId: string): Promise<ApiResponse<Task[]>> {
    const tasksResult = await this.getUserTasks(userId);
    if (!tasksResult.success) {
      return tasksResult;
    }

    const overdueTasks = tasksResult.data.filter(task => 
      isTaskOverdue(task) && task.status !== 'completed'
    );

    return {
      success: true,
      data: overdueTasks
    };
  }

  /**
   * 获取即将到期的任务
   */
  async getDueSoonTasks(userId: string, hoursThreshold: number = 24): Promise<ApiResponse<Task[]>> {
    const tasksResult = await this.getUserTasks(userId);
    if (!tasksResult.success) {
      return tasksResult;
    }

    const dueSoonTasks = tasksResult.data.filter(task => 
      isTaskDueSoon(task, hoursThreshold) && task.status !== 'completed'
    );

    return {
      success: true,
      data: dueSoonTasks
    };
  }

  // ============================================================================
  // 任务统计
  // ============================================================================

  /**
   * 获取任务统计信息
   */
  async getTaskStatistics(userId: string): Promise<ApiResponse<TaskStatistics>> {
    const tasksResult = await this.getUserTasks(userId);
    if (!tasksResult.success) {
      return {
        success: false,
        error: tasksResult.error,
        data: null as any
      };
    }

    const tasks = tasksResult.data;
    
    // 按状态统计
    const byStatus = tasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    }, {} as Record<TaskStatus, number>);

    // 按分类统计
    const byCategory = tasks.reduce((acc, task) => {
      acc[task.category] = (acc[task.category] || 0) + 1;
      return acc;
    }, {} as Record<TaskCategory, number>);

    // 按象限统计
    const byQuadrant = tasks.reduce((acc, task) => {
      const quadrant = classifyQuadrant(task.importance, task.urgency);
      acc[quadrant] = (acc[quadrant] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    // 过期和即将到期任务数量
    const overdue = tasks.filter(task => isTaskOverdue(task) && task.status !== 'completed').length;
    const dueSoon = tasks.filter(task => isTaskDueSoon(task) && task.status !== 'completed').length;

    // 平均完成时间（这里简化处理，实际应该从完成记录中计算）
    const completedTasks = tasks.filter(task => task.status === 'completed');
    const averageCompletionTime = completedTasks.length > 0 
      ? completedTasks.reduce((sum, task) => sum + task.estimatedDuration, 0) / completedTasks.length
      : 0;

    const statistics: TaskStatistics = {
      total: tasks.length,
      byStatus: byStatus as Record<TaskStatus, number>,
      byCategory: byCategory as Record<TaskCategory, number>,
      byQuadrant: byQuadrant as Record<any, number>,
      overdue,
      dueSoon,
      averageCompletionTime
    };

    return {
      success: true,
      data: statistics
    };
  }

  // ============================================================================
  // 批量操作
  // ============================================================================

  /**
   * 批量更新任务状态
   */
  async batchUpdateStatus(taskIds: string[], status: TaskStatus): Promise<ApiResponse<void>> {
    if (!taskIds || taskIds.length === 0) {
      return {
        success: false,
        error: '任务ID列表不能为空',
        data: undefined
      };
    }

    return await this.repository.batchUpdateStatus(taskIds, status);
  }

  /**
   * 批量删除任务
   */
  async batchDeleteTasks(taskIds: string[]): Promise<ApiResponse<void>> {
    if (!taskIds || taskIds.length === 0) {
      return {
        success: false,
        error: '任务ID列表不能为空',
        data: undefined
      };
    }

    try {
      for (const id of taskIds) {
        const result = await this.repository.delete(id);
        if (!result.success) {
          return {
            success: false,
            error: `删除任务 ${id} 失败: ${result.error}`,
            data: undefined
          };
        }
      }

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量删除任务时发生未知错误',
        data: undefined
      };
    }
  }
}
