// 数据库结构检查脚本
const { createClient } = require('@supabase/supabase-js');

// 直接使用配置值（从 .env.local 文件中复制）
const supabaseUrl = 'https://vnkdibuupyelcopkqsev.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZua2RpYnV1cHllbGNvcGtxc2V2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0NjY0NDAsImV4cCI6MjA2ODA0MjQ0MH0.RIIGHnOKWz0dHOLLnQMagF182tZe3viYIanPegeHb2A';

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Supabase 环境变量未配置');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  console.log('🔍 检查数据库连接和表结构...\n');
  
  try {
    // 1. 检查连接
    console.log('1. 测试数据库连接...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('user_profiles')
      .select('count', { count: 'exact', head: true });
    
    if (connectionError) {
      console.error('❌ 数据库连接失败:', connectionError.message);
      return;
    }
    console.log('✅ 数据库连接成功');
    
    // 2. 检查 user_profiles 表结构
    console.log('\n2. 检查 user_profiles 表结构...');
    
    // 尝试插入一个测试记录来检查字段
    const testUserId = '00000000-0000-0000-0000-000000000000';
    const testData = {
      id: testUserId,
      email: '<EMAIL>',
      timezone: 'UTC',
      work_hours: { start: '09:00', end: '18:00' }
    };
    
    // 先删除可能存在的测试记录
    await supabase.from('user_profiles').delete().eq('id', testUserId);
    
    // 测试基本字段
    console.log('   测试基本字段...');
    const { error: basicError } = await supabase
      .from('user_profiles')
      .insert(testData);
    
    if (basicError) {
      console.error('❌ 基本字段测试失败:', basicError.message);
      console.log('   可能的问题: 表不存在或基本字段缺失');
      return;
    }
    console.log('✅ 基本字段存在');
    
    // 测试扩展字段
    console.log('   测试扩展字段...');
    const extendedData = {
      ...testData,
      time_config: { test: true },
      onboarding_completed: true
    };
    
    const { error: extendedError } = await supabase
      .from('user_profiles')
      .upsert(extendedData);
    
    if (extendedError) {
      console.log('⚠️  扩展字段不存在:', extendedError.message);
      console.log('   需要运行数据库迁移脚本');
      
      // 显示迁移建议
      console.log('\n📋 建议执行以下 SQL 迁移:');
      console.log('```sql');
      console.log('ALTER TABLE user_profiles ');
      console.log('ADD COLUMN IF NOT EXISTS time_config JSONB DEFAULT \'{}\',');
      console.log('ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE;');
      console.log('```');
    } else {
      console.log('✅ 扩展字段存在');
    }
    
    // 清理测试数据
    await supabase.from('user_profiles').delete().eq('id', testUserId);
    
    // 3. 检查其他必要的表
    console.log('\n3. 检查其他表...');
    const tables = ['tasks', 'daily_stats'];
    
    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('count', { count: 'exact', head: true });
        
        if (error) {
          console.log(`⚠️  表 ${table} 不存在或无法访问:`, error.message);
        } else {
          console.log(`✅ 表 ${table} 存在`);
        }
      } catch (err) {
        console.log(`❌ 检查表 ${table} 时出错:`, err.message);
      }
    }
    
    console.log('\n🎉 数据库检查完成!');
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error);
  }
}

// 运行检查
checkDatabase().then(() => {
  console.log('\n检查完成，可以关闭此脚本。');
}).catch(console.error);
