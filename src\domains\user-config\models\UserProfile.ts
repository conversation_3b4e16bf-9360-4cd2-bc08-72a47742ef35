/**
 * 用户配置模型
 * 定义用户配置的数据结构和相关类型
 */

import {
  UserProfile,
  UserTimeConfig,
  WorkHours,
  FixedTimeSlot,
  CategoryPreferences,
  CategoryPreference
} from '@/shared';

/**
 * 数据库用户配置记录类型
 */
export interface UserProfileRecord {
  id: string;
  email: string;
  name?: string;
  timezone: string;
  work_hours: {
    start: string;
    end: string;
    days?: string[];
  };
  category_ratios?: {
    work: number;
    improvement: number;
    entertainment: number;
  };
  time_config?: any;
  onboarding_completed: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * 用户配置创建请求类型
 */
export interface CreateUserProfileRequest {
  id: string;
  email: string;
  name?: string;
  timezone?: string;
  workHours?: WorkHours;
  timeConfig?: UserTimeConfig;
  onboardingCompleted?: boolean;
}

/**
 * 用户配置更新请求类型
 */
export interface UpdateUserProfileRequest {
  id: string;
  name?: string;
  timezone?: string;
  workHours?: WorkHours;
  timeConfig?: UserTimeConfig;
  onboardingCompleted?: boolean;
}

/**
 * 引导配置步骤类型
 */
export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: string;
  isCompleted: boolean;
  isRequired: boolean;
}

/**
 * 引导配置状态类型
 */
export interface OnboardingState {
  currentStep: number;
  totalSteps: number;
  steps: OnboardingStep[];
  isCompleted: boolean;
  timeConfig: Partial<UserTimeConfig>;
}

/**
 * 时间偏好设置类型
 */
export interface TimePreferences {
  workStart: string;
  workEnd: string;
  workDays: string[];
  sleepStart: string;
  sleepEnd: string;
  preferredWorkHours: 'morning' | 'afternoon' | 'evening';
  maxContinuousWork: number;
  breakInterval: number;
  commuteToWork: number;
  commuteFromWork: number;
}

/**
 * 通知设置类型
 */
export interface NotificationSettings {
  email: boolean;
  push: boolean;
  desktop: boolean;
  taskReminders: boolean;
  scheduleUpdates: boolean;
  weeklyReports: boolean;
  reminderMinutes: number[];
}

/**
 * 用户偏好设置类型
 */
export interface UserPreferences {
  id: string;
  userId: string;
  preferredWorkHours: 'morning' | 'afternoon' | 'evening';
  maxContinuousWork: number;
  breakInterval: number;
  notificationSettings: NotificationSettings;
  adjustmentStrategy: 'conservative' | 'balanced' | 'aggressive';
  autoAdjustEnabled: boolean;
  theme: 'light' | 'dark' | 'system';
  language: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
}

/**
 * 用户统计信息类型
 */
export interface UserStats {
  userId: string;
  totalTasks: number;
  completedTasks: number;
  completionRate: number;
  averageTaskDuration: number;
  mostProductiveHour: number;
  streakDays: number;
  totalTimeSpent: number;
  categoryBreakdown: {
    work: number;
    improvement: number;
    entertainment: number;
  };
}

/**
 * 将数据库记录转换为用户配置对象
 */
export function mapRecordToUserProfile(record: UserProfileRecord): UserProfile {
  return {
    id: record.id,
    email: record.email,
    name: record.name,
    timezone: record.timezone,
    workHours: {
      start: record.work_hours.start,
      end: record.work_hours.end,
      days: record.work_hours.days
    },
    timeConfig: record.time_config || getDefaultTimeConfig(),
    onboardingCompleted: record.onboarding_completed,
    createdAt: new Date(record.created_at),
    updatedAt: new Date(record.updated_at)
  };
}

/**
 * 将用户配置对象转换为数据库记录
 */
export function mapUserProfileToRecord(profile: UserProfile | CreateUserProfileRequest): Omit<UserProfileRecord, 'created_at' | 'updated_at'> {
  return {
    id: profile.id,
    email: profile.email,
    name: profile.name,
    timezone: profile.timezone || 'UTC',
    work_hours: {
      start: profile.workHours?.start || '09:00',
      end: profile.workHours?.end || '18:00',
      days: profile.workHours?.days
    },
    time_config: (profile as any).timeConfig,
    onboarding_completed: (profile as any).onboardingCompleted || false
  };
}

/**
 * 获取默认时间配置
 */
export function getDefaultTimeConfig(): UserTimeConfig {
  return {
    workStart: '09:00',
    workEnd: '18:00',
    workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    sleepStart: '23:00',
    sleepEnd: '07:00',
    fixedSlots: [
      { start: '07:00', end: '08:00', type: 'personal', label: '晨间例行' },
      { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' },
      { start: '18:30', end: '19:30', type: 'meal', label: '晚餐时间' }
    ],
    commuteToWork: 30,
    commuteFromWork: 30,
    preferredWorkHours: 'morning',
    maxContinuousWork: 120,
    breakInterval: 25,
    categoryPreferences: {
      work: { 
        preferredTimes: ['09:00-12:00', '14:00-18:00'], 
        maxDaily: 480 
      },
      improvement: { 
        preferredTimes: ['07:00-09:00', '19:00-21:00'], 
        maxDaily: 120 
      },
      entertainment: { 
        preferredTimes: ['20:00-22:00'], 
        maxDaily: 180 
      }
    }
  };
}

/**
 * 获取默认通知设置
 */
export function getDefaultNotificationSettings(): NotificationSettings {
  return {
    email: true,
    push: true,
    desktop: false,
    taskReminders: true,
    scheduleUpdates: true,
    weeklyReports: false,
    reminderMinutes: [15, 60, 1440] // 15分钟、1小时、1天前
  };
}

/**
 * 获取默认用户偏好
 */
export function getDefaultUserPreferences(userId: string): UserPreferences {
  return {
    id: '',
    userId,
    preferredWorkHours: 'morning',
    maxContinuousWork: 120,
    breakInterval: 25,
    notificationSettings: getDefaultNotificationSettings(),
    adjustmentStrategy: 'balanced',
    autoAdjustEnabled: true,
    theme: 'system',
    language: 'zh-CN',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h'
  };
}

/**
 * 验证时间配置的有效性
 */
export function validateTimeConfig(config: Partial<UserTimeConfig>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 验证工作时间
  if (config.workStart && config.workEnd) {
    const startTime = new Date(`2000-01-01 ${config.workStart}`);
    const endTime = new Date(`2000-01-01 ${config.workEnd}`);
    
    if (startTime >= endTime) {
      errors.push('工作结束时间必须晚于开始时间');
    }
  }

  // 验证睡眠时间
  if (config.sleepStart && config.sleepEnd) {
    const sleepStart = new Date(`2000-01-01 ${config.sleepStart}`);
    const sleepEnd = new Date(`2000-01-02 ${config.sleepEnd}`); // 次日
    
    const sleepDuration = (sleepEnd.getTime() - sleepStart.getTime()) / (1000 * 60 * 60);
    if (sleepDuration < 6 || sleepDuration > 12) {
      errors.push('睡眠时间应该在6-12小时之间');
    }
  }

  // 验证工作日
  if (config.workDays && config.workDays.length === 0) {
    errors.push('至少需要选择一个工作日');
  }

  // 验证通勤时间
  if (config.commuteToWork !== undefined && (config.commuteToWork < 0 || config.commuteToWork > 180)) {
    errors.push('通勤时间应该在0-180分钟之间');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
