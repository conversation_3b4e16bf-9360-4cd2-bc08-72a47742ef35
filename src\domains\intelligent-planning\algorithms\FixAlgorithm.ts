/**
 * 修复算法
 * 检测和处理推迟的任务，提供修复建议
 */

import {
  Task,
  PostponedTaskAlert,
  TaskStatus,
  Priority,
  classifyQuadrant,
  isTaskOverdue,
  getDaysDifference,
  getRelativeTime,
  QUADRANTS
} from '@/shared';

export class FixAlgorithm {

  /**
   * 分析推迟的任务
   */
  analyzePostponedTasks(tasks: Task[]): PostponedTaskAlert[] {
    const postponedTasks = tasks.filter(task => 
      task.postponeCount > 0 || 
      task.status === 'postponed' ||
      isTaskOverdue(task)
    );

    return postponedTasks.map(task => this.createPostponedAlert(task));
  }

  /**
   * 创建推迟任务提醒
   */
  private createPostponedAlert(task: Task): PostponedTaskAlert {
    const daysSinceCreated = getDaysDifference(task.createdAt, new Date());
    const severity = this.calculateSeverity(task);
    const recommendation = this.generateRecommendation(task);

    return {
      taskId: task.id,
      task,
      postponeCount: task.postponeCount,
      lastPostponeDate: task.updatedAt,
      severity,
      recommendation
    };
  }

  /**
   * 计算严重程度
   */
  private calculateSeverity(task: Task): 'low' | 'medium' | 'high' {
    const quadrant = classifyQuadrant(task.importance, task.urgency);
    const isOverdue = isTaskOverdue(task);
    const postponeCount = task.postponeCount;

    // 已过期的任务
    if (isOverdue) {
      if (quadrant === QUADRANTS.URGENT_IMPORTANT) {
        return 'high';
      } else if (quadrant === QUADRANTS.IMPORTANT_NOT_URGENT || 
                 quadrant === QUADRANTS.URGENT_NOT_IMPORTANT) {
        return 'medium';
      } else {
        return 'low';
      }
    }

    // 根据推迟次数判断
    if (postponeCount >= 3) {
      return quadrant <= 2 ? 'high' : 'medium';
    } else if (postponeCount >= 2) {
      return quadrant === 1 ? 'high' : 'medium';
    } else {
      return quadrant === 1 ? 'medium' : 'low';
    }
  }

  /**
   * 生成修复建议
   */
  private generateRecommendation(task: Task): string {
    const quadrant = classifyQuadrant(task.importance, task.urgency);
    const isOverdue = isTaskOverdue(task);
    const postponeCount = task.postponeCount;

    if (isOverdue) {
      return this.getOverdueRecommendation(task, quadrant);
    }

    if (postponeCount >= 3) {
      return this.getHighPostponeRecommendation(task, quadrant);
    }

    if (postponeCount >= 1) {
      return this.getPostponeRecommendation(task, quadrant);
    }

    return '建议尽快处理此任务';
  }

  /**
   * 获取过期任务建议
   */
  private getOverdueRecommendation(task: Task, quadrant: number): string {
    const daysPastDeadline = Math.abs(getDaysDifference(task.deadline, new Date()));
    
    switch (quadrant) {
      case QUADRANTS.URGENT_IMPORTANT:
        return `🚨 任务已过期${daysPastDeadline}天，需要立即处理！考虑重新评估截止时间或寻求帮助。`;
      
      case QUADRANTS.IMPORTANT_NOT_URGENT:
        return `⚠️ 重要任务已过期${daysPastDeadline}天，建议重新安排优先级，尽快完成。`;
      
      case QUADRANTS.URGENT_NOT_IMPORTANT:
        return `⏰ 紧急任务已过期${daysPastDeadline}天，考虑委托他人处理或重新评估必要性。`;
      
      default:
        return `📅 任务已过期${daysPastDeadline}天，建议评估是否仍需完成，或者删除此任务。`;
    }
  }

  /**
   * 获取高频推迟任务建议
   */
  private getHighPostponeRecommendation(task: Task, quadrant: number): string {
    switch (quadrant) {
      case QUADRANTS.URGENT_IMPORTANT:
        return `🔥 此任务已推迟${task.postponeCount}次，建议分解为更小的子任务，或寻求帮助完成。`;
      
      case QUADRANTS.IMPORTANT_NOT_URGENT:
        return `📋 重要任务推迟${task.postponeCount}次，建议设定固定时间块专门处理，避免再次推迟。`;
      
      case QUADRANTS.URGENT_NOT_IMPORTANT:
        return `🤝 紧急任务推迟${task.postponeCount}次，强烈建议委托他人处理或使用自动化工具。`;
      
      default:
        return `🤔 任务推迟${task.postponeCount}次，建议重新评估其必要性，考虑删除或降低优先级。`;
    }
  }

  /**
   * 获取一般推迟任务建议
   */
  private getPostponeRecommendation(task: Task, quadrant: number): string {
    switch (quadrant) {
      case QUADRANTS.URGENT_IMPORTANT:
        return '🎯 高优先级任务，建议立即安排时间处理，避免进一步推迟。';
      
      case QUADRANTS.IMPORTANT_NOT_URGENT:
        return '📅 重要任务，建议在日程中安排固定时间，确保按时完成。';
      
      case QUADRANTS.URGENT_NOT_IMPORTANT:
        return '⚡ 考虑委托他人处理，或寻找更高效的解决方案。';
      
      default:
        return '💭 评估任务的实际价值，考虑是否需要继续保留。';
    }
  }

  /**
   * 获取推迟任务的统计信息
   */
  getPostponedTasksStats(tasks: Task[]): {
    totalPostponed: number;
    byCategory: Record<string, number>;
    bySeverity: Record<string, number>;
    averagePostponeCount: number;
  } {
    const postponedTasks = tasks.filter(task => 
      task.postponeCount > 0 || task.status === 'postponed'
    );

    const byCategory = postponedTasks.reduce((acc, task) => {
      acc[task.category] = (acc[task.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const alerts = this.analyzePostponedTasks(postponedTasks);
    const bySeverity = alerts.reduce((acc, alert) => {
      acc[alert.severity] = (acc[alert.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalPostponeCount = postponedTasks.reduce((sum, task) => sum + task.postponeCount, 0);
    const averagePostponeCount = postponedTasks.length > 0 ? 
      Math.round((totalPostponeCount / postponedTasks.length) * 10) / 10 : 0;

    return {
      totalPostponed: postponedTasks.length,
      byCategory,
      bySeverity,
      averagePostponeCount
    };
  }

  /**
   * 建议任务重新安排策略
   */
  suggestRescheduleStrategy(task: Task): {
    strategy: string;
    newDeadline?: Date;
    breakDown?: string[];
    delegation?: string;
  } {
    const quadrant = classifyQuadrant(task.importance, task.urgency);
    const postponeCount = task.postponeCount;

    // 高频推迟的任务需要特殊处理
    if (postponeCount >= 3) {
      return this.getHighPostponeStrategy(task, quadrant);
    }

    // 一般推迟任务的策略
    return this.getGeneralStrategy(task, quadrant);
  }

  /**
   * 获取高频推迟任务的策略
   */
  private getHighPostponeStrategy(task: Task, quadrant: number): any {
    switch (quadrant) {
      case QUADRANTS.URGENT_IMPORTANT:
        return {
          strategy: 'immediate_action',
          breakDown: [
            '将任务分解为15-30分钟的小块',
            '立即开始第一个小任务',
            '寻求同事或朋友的帮助',
            '移除所有干扰因素'
          ]
        };

      case QUADRANTS.IMPORTANT_NOT_URGENT:
        const newDeadline = new Date();
        newDeadline.setDate(newDeadline.getDate() + 7);
        return {
          strategy: 'scheduled_focus',
          newDeadline,
          breakDown: [
            '设定每日固定时间处理',
            '使用番茄工作法',
            '设置进度检查点',
            '奖励机制激励完成'
          ]
        };

      case QUADRANTS.URGENT_NOT_IMPORTANT:
        return {
          strategy: 'delegate_or_automate',
          delegation: '寻找可以委托的人员或自动化工具',
          breakDown: [
            '评估委托的可能性',
            '寻找自动化解决方案',
            '如无法委托，快速批量处理'
          ]
        };

      default:
        return {
          strategy: 'eliminate_or_defer',
          breakDown: [
            '重新评估任务的必要性',
            '考虑完全删除此任务',
            '如必须保留，设定更宽松的时间线'
          ]
        };
    }
  }

  /**
   * 获取一般任务的重新安排策略
   */
  private getGeneralStrategy(task: Task, quadrant: number): any {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    switch (quadrant) {
      case QUADRANTS.URGENT_IMPORTANT:
        return {
          strategy: 'priority_scheduling',
          newDeadline: tomorrow,
          breakDown: ['安排在最佳工作时间', '清除其他干扰', '专注完成']
        };

      case QUADRANTS.IMPORTANT_NOT_URGENT:
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        return {
          strategy: 'planned_execution',
          newDeadline: nextWeek,
          breakDown: ['制定详细计划', '分阶段执行', '定期检查进度']
        };

      default:
        return {
          strategy: 'flexible_scheduling',
          breakDown: ['安排在空闲时间', '与其他类似任务批量处理']
        };
    }
  }

  /**
   * 检查任务是否需要紧急关注
   */
  needsUrgentAttention(task: Task): boolean {
    const isOverdue = isTaskOverdue(task);
    const quadrant = classifyQuadrant(task.importance, task.urgency);
    const highPostponeCount = task.postponeCount >= 3;

    return isOverdue || 
           (quadrant === QUADRANTS.URGENT_IMPORTANT && task.postponeCount >= 1) ||
           (highPostponeCount && quadrant <= 2);
  }

  /**
   * 生成修复行动计划
   */
  generateActionPlan(alerts: PostponedTaskAlert[]): string[] {
    const actionPlan: string[] = [];
    
    const highSeverityTasks = alerts.filter(alert => alert.severity === 'high');
    const mediumSeverityTasks = alerts.filter(alert => alert.severity === 'medium');
    
    if (highSeverityTasks.length > 0) {
      actionPlan.push(`🚨 立即处理 ${highSeverityTasks.length} 个高优先级推迟任务`);
    }
    
    if (mediumSeverityTasks.length > 0) {
      actionPlan.push(`⚠️ 本周内处理 ${mediumSeverityTasks.length} 个中等优先级推迟任务`);
    }
    
    if (alerts.length > 5) {
      actionPlan.push('📋 考虑重新评估任务管理策略，避免过多任务推迟');
    }
    
    return actionPlan;
  }
}
