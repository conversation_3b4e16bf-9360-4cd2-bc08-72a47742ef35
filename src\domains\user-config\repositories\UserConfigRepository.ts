/**
 * 用户配置仓储层
 * 负责用户配置数据的持久化操作
 */

import { supabase } from '@/lib/supabase';
import {
  UserProfile,
  ApiResponse,
  createAppError,
  ERROR_CODES
} from '@/shared';

import {
  UserProfileRecord,
  CreateUserProfileRequest,
  UpdateUserProfileRequest,
  UserPreferences,
  UserStats,
  mapRecordToUserProfile,
  mapUserProfileToRecord,
  getDefaultUserPreferences
} from '../models/UserProfile';

export class UserConfigRepository {

  /**
   * 根据ID获取用户配置
   */
  async findById(userId: string): Promise<ApiResponse<UserProfile | null>> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return {
            success: true,
            data: null
          };
        }
        return {
          success: false,
          error: `获取用户配置失败: ${error.message}`,
          data: null
        };
      }

      const profile = mapRecordToUserProfile(data);

      return {
        success: true,
        data: profile
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取用户配置时发生未知错误',
        data: null
      };
    }
  }

  /**
   * 创建用户配置
   */
  async create(profileData: CreateUserProfileRequest): Promise<ApiResponse<UserProfile>> {
    try {
      const record = mapUserProfileToRecord(profileData);

      const { data, error } = await supabase
        .from('user_profiles')
        .insert(record)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: `创建用户配置失败: ${error.message}`,
          data: null as any
        };
      }

      const profile = mapRecordToUserProfile(data);

      return {
        success: true,
        data: profile
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建用户配置时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 更新用户配置
   */
  async update(updateData: UpdateUserProfileRequest): Promise<ApiResponse<UserProfile>> {
    try {
      const { id, ...updates } = updateData;
      
      // 构建更新数据
      const updateRecord: Partial<UserProfileRecord> = {};
      
      if (updates.name !== undefined) updateRecord.name = updates.name;
      if (updates.timezone !== undefined) updateRecord.timezone = updates.timezone;
      if (updates.workHours !== undefined) {
        updateRecord.work_hours = {
          start: updates.workHours.start,
          end: updates.workHours.end,
          days: updates.workHours.days
        };
      }
      if (updates.timeConfig !== undefined) updateRecord.time_config = updates.timeConfig;
      if (updates.onboardingCompleted !== undefined) updateRecord.onboarding_completed = updates.onboardingCompleted;

      const { data, error } = await supabase
        .from('user_profiles')
        .update(updateRecord)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: `更新用户配置失败: ${error.message}`,
          data: null as any
        };
      }

      const profile = mapRecordToUserProfile(data);

      return {
        success: true,
        data: profile
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新用户配置时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 保存或更新用户配置（upsert）
   */
  async upsert(profileData: CreateUserProfileRequest): Promise<ApiResponse<UserProfile>> {
    try {
      const record = mapUserProfileToRecord(profileData);

      const { data, error } = await supabase
        .from('user_profiles')
        .upsert(record, { onConflict: 'id' })
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: `保存用户配置失败: ${error.message}`,
          data: null as any
        };
      }

      const profile = mapRecordToUserProfile(data);

      return {
        success: true,
        data: profile
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '保存用户配置时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 删除用户配置
   */
  async delete(userId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .delete()
        .eq('id', userId);

      if (error) {
        return {
          success: false,
          error: `删除用户配置失败: ${error.message}`,
          data: undefined
        };
      }

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除用户配置时发生未知错误',
        data: undefined
      };
    }
  }

  /**
   * 检查用户是否完成引导
   */
  async isOnboardingCompleted(userId: string): Promise<ApiResponse<boolean>> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('onboarding_completed')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return {
            success: true,
            data: false
          };
        }
        return {
          success: false,
          error: `检查引导状态失败: ${error.message}`,
          data: false
        };
      }

      return {
        success: true,
        data: data.onboarding_completed || false
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '检查引导状态时发生未知错误',
        data: false
      };
    }
  }

  /**
   * 标记引导完成
   */
  async markOnboardingCompleted(userId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({ onboarding_completed: true })
        .eq('id', userId);

      if (error) {
        return {
          success: false,
          error: `标记引导完成失败: ${error.message}`,
          data: undefined
        };
      }

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '标记引导完成时发生未知错误',
        data: undefined
      };
    }
  }

  /**
   * 获取用户偏好设置
   */
  async getUserPreferences(userId: string): Promise<ApiResponse<UserPreferences>> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // 如果没有找到偏好设置，返回默认值
          return {
            success: true,
            data: getDefaultUserPreferences(userId)
          };
        }
        return {
          success: false,
          error: `获取用户偏好失败: ${error.message}`,
          data: null as any
        };
      }

      const preferences: UserPreferences = {
        id: data.id,
        userId: data.user_id,
        preferredWorkHours: data.preferred_work_hours,
        maxContinuousWork: data.max_continuous_work,
        breakInterval: data.break_interval,
        notificationSettings: data.notification_settings,
        adjustmentStrategy: data.adjustment_strategy,
        autoAdjustEnabled: data.auto_adjust_enabled,
        theme: data.theme || 'system',
        language: data.language || 'zh-CN',
        dateFormat: data.date_format || 'YYYY-MM-DD',
        timeFormat: data.time_format || '24h'
      };

      return {
        success: true,
        data: preferences
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取用户偏好时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<ApiResponse<UserPreferences>> {
    try {
      const updateData: any = {};
      
      if (preferences.preferredWorkHours !== undefined) updateData.preferred_work_hours = preferences.preferredWorkHours;
      if (preferences.maxContinuousWork !== undefined) updateData.max_continuous_work = preferences.maxContinuousWork;
      if (preferences.breakInterval !== undefined) updateData.break_interval = preferences.breakInterval;
      if (preferences.notificationSettings !== undefined) updateData.notification_settings = preferences.notificationSettings;
      if (preferences.adjustmentStrategy !== undefined) updateData.adjustment_strategy = preferences.adjustmentStrategy;
      if (preferences.autoAdjustEnabled !== undefined) updateData.auto_adjust_enabled = preferences.autoAdjustEnabled;
      if (preferences.theme !== undefined) updateData.theme = preferences.theme;
      if (preferences.language !== undefined) updateData.language = preferences.language;
      if (preferences.dateFormat !== undefined) updateData.date_format = preferences.dateFormat;
      if (preferences.timeFormat !== undefined) updateData.time_format = preferences.timeFormat;

      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: preferences.userId,
          ...updateData
        }, { onConflict: 'user_id' })
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: `更新用户偏好失败: ${error.message}`,
          data: null as any
        };
      }

      const updatedPreferences: UserPreferences = {
        id: data.id,
        userId: data.user_id,
        preferredWorkHours: data.preferred_work_hours,
        maxContinuousWork: data.max_continuous_work,
        breakInterval: data.break_interval,
        notificationSettings: data.notification_settings,
        adjustmentStrategy: data.adjustment_strategy,
        autoAdjustEnabled: data.auto_adjust_enabled,
        theme: data.theme,
        language: data.language,
        dateFormat: data.date_format,
        timeFormat: data.time_format
      };

      return {
        success: true,
        data: updatedPreferences
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新用户偏好时发生未知错误',
        data: null as any
      };
    }
  }
}
