/**
 * 用户配置域统一导出
 * 提供用户配置管理相关的所有功能
 */

// ============================================================================
// 模型导出
// ============================================================================

export type {
  UserProfileRecord,
  CreateUserProfileRequest,
  UpdateUserProfileRequest,
  OnboardingStep,
  OnboardingState,
  TimePreferences,
  NotificationSettings,
  UserPreferences,
  UserStats
} from './models/UserProfile';

export {
  mapRecordToUserProfile,
  mapUserProfileToRecord,
  getDefaultTimeConfig,
  getDefaultNotificationSettings,
  getDefaultUserPreferences,
  validateTimeConfig
} from './models/UserProfile';

// ============================================================================
// 仓储导出
// ============================================================================

export { UserConfigRepository } from './repositories/UserConfigRepository';

// ============================================================================
// 服务导出
// ============================================================================

export { UserConfigService } from './services/UserConfigService';
export { OnboardingService } from './services/OnboardingService';

// ============================================================================
// 便捷实例创建
// ============================================================================

/**
 * 创建用户配置服务实例
 */
export function createUserConfigService(): UserConfigService {
  return new UserConfigService();
}

/**
 * 创建引导服务实例
 */
export function createOnboardingService(): OnboardingService {
  return new OnboardingService();
}

/**
 * 创建用户配置仓储实例
 */
export function createUserConfigRepository(): UserConfigRepository {
  return new UserConfigRepository();
}

// ============================================================================
// 默认实例（单例模式）
// ============================================================================

let defaultUserConfigService: UserConfigService | null = null;
let defaultOnboardingService: OnboardingService | null = null;

/**
 * 获取默认的用户配置服务实例（单例）
 */
export function getDefaultUserConfigService(): UserConfigService {
  if (!defaultUserConfigService) {
    defaultUserConfigService = new UserConfigService();
  }
  return defaultUserConfigService;
}

/**
 * 获取默认的引导服务实例（单例）
 */
export function getDefaultOnboardingService(): OnboardingService {
  if (!defaultOnboardingService) {
    defaultOnboardingService = new OnboardingService();
  }
  return defaultOnboardingService;
}

/**
 * 重置默认实例
 */
export function resetDefaultUserConfigService(): void {
  defaultUserConfigService = null;
}

export function resetDefaultOnboardingService(): void {
  defaultOnboardingService = null;
}

// ============================================================================
// 类型重新导出（从 shared 模块）
// ============================================================================

export type {
  UserProfile,
  UserTimeConfig,
  WorkHours,
  FixedTimeSlot,
  CategoryPreferences,
  CategoryPreference
} from '@/shared';

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 验证用户配置服务的健康状态
 */
export async function validateUserConfigServiceHealth(): Promise<{
  isHealthy: boolean;
  errors: string[];
  timestamp: Date;
}> {
  const errors: string[] = [];
  let isHealthy = true;

  try {
    const service = getDefaultUserConfigService();
    const onboardingService = getDefaultOnboardingService();
    
    // 测试引导服务
    const onboardingState = onboardingService.initializeOnboardingState();
    if (!onboardingState || !onboardingState.steps) {
      errors.push('引导服务初始化失败');
      isHealthy = false;
    }
    
    // 测试配置验证
    const testConfig = getDefaultTimeConfig();
    const validation = validateTimeConfig(testConfig);
    if (!validation.isValid) {
      errors.push('默认配置验证失败');
      isHealthy = false;
    }
  } catch (error) {
    errors.push(`用户配置服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    isHealthy = false;
  }

  return {
    isHealthy,
    errors,
    timestamp: new Date()
  };
}

/**
 * 获取用户配置域的功能清单
 */
export function getUserConfigDomainFeatures(): {
  models: string[];
  repositories: string[];
  services: string[];
  capabilities: string[];
} {
  return {
    models: [
      'UserProfile - 用户配置模型',
      'UserProfileRecord - 数据库记录映射',
      'OnboardingStep - 引导步骤定义',
      'OnboardingState - 引导状态管理',
      'UserPreferences - 用户偏好设置',
      'NotificationSettings - 通知设置',
      'TimePreferences - 时间偏好配置'
    ],
    repositories: [
      'UserConfigRepository - 用户配置数据持久化'
    ],
    services: [
      'UserConfigService - 用户配置管理服务',
      'OnboardingService - 用户引导流程服务'
    ],
    capabilities: [
      '完整的用户配置 CRUD 操作',
      '智能的用户引导流程',
      '时间配置验证和管理',
      '用户偏好设置管理',
      '引导步骤验证和导航',
      '配置导入导出功能',
      '默认配置管理',
      '时区和本地化支持',
      '通知设置管理',
      '配置健康检查',
      '引导完成状态跟踪',
      '个性化设置建议'
    ]
  };
}

/**
 * 获取用户配置的性能基准测试
 */
export async function runUserConfigBenchmark(): Promise<{
  configValidationTime: number;
  onboardingInitTime: number;
  preferencesValidationTime: number;
  totalTime: number;
}> {
  const service = createUserConfigService();
  const onboardingService = createOnboardingService();
  
  const startTime = Date.now();
  
  // 测试配置验证性能
  const configValidationStart = Date.now();
  const testConfig = getDefaultTimeConfig();
  validateTimeConfig(testConfig);
  const configValidationTime = Date.now() - configValidationStart;

  // 测试引导初始化性能
  const onboardingInitStart = Date.now();
  onboardingService.initializeOnboardingState();
  const onboardingInitTime = Date.now() - onboardingInitStart;

  // 测试偏好验证性能
  const preferencesValidationStart = Date.now();
  const testPreferences = getDefaultUserPreferences('test-user');
  service['validateUserPreferences'](testPreferences);
  const preferencesValidationTime = Date.now() - preferencesValidationStart;

  const totalTime = Date.now() - startTime;

  return {
    configValidationTime,
    onboardingInitTime,
    preferencesValidationTime,
    totalTime
  };
}

/**
 * 获取用户配置域的配置选项
 */
export function getUserConfigDomainConfig(): {
  maxOnboardingSteps: number;
  defaultTimezone: string;
  supportedLanguages: string[];
  supportedThemes: string[];
  maxFixedSlots: number;
} {
  return {
    maxOnboardingSteps: 10,
    defaultTimezone: 'UTC',
    supportedLanguages: ['zh-CN', 'en-US'],
    supportedThemes: ['light', 'dark', 'system'],
    maxFixedSlots: 10
  };
}

/**
 * 创建用户配置域的示例数据
 */
export function createSampleUserConfigData(): {
  profile: CreateUserProfileRequest;
  preferences: UserPreferences;
} {
  const userId = 'sample-user';
  
  return {
    profile: {
      id: userId,
      email: '<EMAIL>',
      name: '示例用户',
      timezone: 'Asia/Shanghai',
      timeConfig: getDefaultTimeConfig(),
      onboardingCompleted: true
    },
    preferences: getDefaultUserPreferences(userId)
  };
}

/**
 * 获取引导步骤的本地化文本
 */
export function getOnboardingStepTexts(language: string = 'zh-CN'): Record<string, any> {
  const texts = {
    'zh-CN': {
      welcome: {
        title: '欢迎使用 TimeManager',
        description: '让我们花几分钟时间了解您的时间偏好，为您提供个性化的时间管理体验。'
      },
      'work-schedule': {
        title: '工作时间设置',
        description: '设置您的工作时间和工作日，帮助我们更好地安排您的任务。'
      },
      'sleep-schedule': {
        title: '作息时间设置',
        description: '设置您的睡眠时间，确保任务安排不会影响您的休息。'
      }
    },
    'en-US': {
      welcome: {
        title: 'Welcome to TimeManager',
        description: 'Let\'s take a few minutes to understand your time preferences and provide you with a personalized time management experience.'
      },
      'work-schedule': {
        title: 'Work Schedule Setup',
        description: 'Set your work hours and work days to help us better arrange your tasks.'
      },
      'sleep-schedule': {
        title: 'Sleep Schedule Setup',
        description: 'Set your sleep time to ensure task scheduling doesn\'t interfere with your rest.'
      }
    }
  };

  return texts[language as keyof typeof texts] || texts['zh-CN'];
}
