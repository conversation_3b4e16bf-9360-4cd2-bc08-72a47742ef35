/**
 * 生活平衡算法
 * 分析用户的时间分配，提供生活平衡建议
 */

import {
  Task,
  DailyStats,
  BalanceAnalysis,
  WeeklyStats,
  CategoryRatios,
  TaskCategory,
  TASK_CATEGORIES,
  getStartOfWeek,
  getEndOfWeek,
  formatDate
} from '@/shared';

export class BalanceAlgorithm {

  /**
   * 分析用户的生活平衡状况
   */
  async analyzeBalance(userId: string, date: Date = new Date()): Promise<BalanceAnalysis> {
    // 获取本周的统计数据
    const weeklyStats = await this.calculateWeeklyStats(userId, date);
    
    // 计算分类比例
    const categoryRatios = this.calculateCategoryRatios(weeklyStats.totalTime);
    
    // 计算平衡分数
    const balanceScore = this.calculateBalanceScore(categoryRatios);
    
    // 生成建议
    const recommendations = this.generateRecommendations(categoryRatios, weeklyStats);

    return {
      userId,
      date,
      categoryRatios,
      weeklyStats,
      recommendations,
      balanceScore
    };
  }

  /**
   * 计算本周统计数据
   */
  private async calculateWeeklyStats(userId: string, date: Date): Promise<WeeklyStats> {
    const startOfWeek = getStartOfWeek(date);
    const endOfWeek = getEndOfWeek(date);
    
    // 这里应该从数据库获取数据，暂时使用模拟数据
    const mockStats: CategoryRatios = {
      work: 2400, // 40小时 * 60分钟
      improvement: 420, // 7小时
      entertainment: 600 // 10小时
    };

    const totalMinutes = mockStats.work + mockStats.improvement + mockStats.entertainment;
    
    const averageDaily: CategoryRatios = {
      work: mockStats.work / 7,
      improvement: mockStats.improvement / 7,
      entertainment: mockStats.entertainment / 7
    };

    // 计算趋势（这里简化处理）
    const trend = this.calculateTrend(mockStats);

    return {
      totalTime: mockStats,
      averageDaily,
      trend
    };
  }

  /**
   * 计算分类比例
   */
  private calculateCategoryRatios(totalTime: CategoryRatios): CategoryRatios {
    const total = totalTime.work + totalTime.improvement + totalTime.entertainment;
    
    if (total === 0) {
      return { work: 0, improvement: 0, entertainment: 0 };
    }

    return {
      work: Math.round((totalTime.work / total) * 100) / 100,
      improvement: Math.round((totalTime.improvement / total) * 100) / 100,
      entertainment: Math.round((totalTime.entertainment / total) * 100) / 100
    };
  }

  /**
   * 计算平衡分数 (0-100)
   */
  private calculateBalanceScore(ratios: CategoryRatios): number {
    // 理想比例：工作60%，提升25%，娱乐15%
    const idealRatios = { work: 0.6, improvement: 0.25, entertainment: 0.15 };
    
    // 计算与理想比例的偏差
    const workDeviation = Math.abs(ratios.work - idealRatios.work);
    const improvementDeviation = Math.abs(ratios.improvement - idealRatios.improvement);
    const entertainmentDeviation = Math.abs(ratios.entertainment - idealRatios.entertainment);
    
    // 总偏差
    const totalDeviation = workDeviation + improvementDeviation + entertainmentDeviation;
    
    // 转换为分数 (偏差越小，分数越高)
    const score = Math.max(0, 100 - (totalDeviation * 100));
    
    return Math.round(score);
  }

  /**
   * 计算趋势
   */
  private calculateTrend(currentStats: CategoryRatios): 'improving' | 'declining' | 'stable' {
    // 这里应该比较本周与上周的数据
    // 暂时返回稳定状态
    return 'stable';
  }

  /**
   * 生成平衡建议
   */
  private generateRecommendations(ratios: CategoryRatios, weeklyStats: WeeklyStats): string[] {
    const recommendations: string[] = [];

    // 工作时间建议
    if (ratios.work > 0.7) {
      recommendations.push('工作时间占比过高，建议适当减少工作量，增加休息时间');
    } else if (ratios.work < 0.5) {
      recommendations.push('工作时间占比较低，可以考虑提高工作效率或增加工作时间');
    }

    // 提升时间建议
    if (ratios.improvement < 0.15) {
      recommendations.push('个人提升时间不足，建议每天安排至少1-2小时用于学习和成长');
    } else if (ratios.improvement > 0.35) {
      recommendations.push('个人提升时间充足，保持良好的学习习惯');
    }

    // 娱乐时间建议
    if (ratios.entertainment < 0.1) {
      recommendations.push('娱乐时间过少，适当的放松有助于提高工作效率');
    } else if (ratios.entertainment > 0.25) {
      recommendations.push('娱乐时间较多，可以考虑将部分时间用于工作或学习');
    }

    // 平衡性建议
    const balanceScore = this.calculateBalanceScore(ratios);
    if (balanceScore >= 80) {
      recommendations.push('时间分配很均衡，继续保持！');
    } else if (balanceScore >= 60) {
      recommendations.push('时间分配基本合理，可以进行微调优化');
    } else {
      recommendations.push('时间分配需要调整，建议重新规划各类活动的时间比例');
    }

    return recommendations;
  }

  /**
   * 更新今日统计
   */
  async updateTodayStats(
    userId: string, 
    category: TaskCategory, 
    duration: number
  ): Promise<void> {
    const today = new Date();
    const dateStr = formatDate(today);

    // 这里应该更新数据库中的统计数据
    console.log(`更新统计: 用户${userId}, 日期${dateStr}, 分类${category}, 时长${duration}分钟`);
    
    // 模拟数据库操作
    // await supabase.from('daily_stats').upsert({
    //   user_id: userId,
    //   date: dateStr,
    //   [category + '_time']: duration
    // });
  }

  /**
   * 获取分类时间建议
   */
  getCategoryTimeRecommendation(category: TaskCategory, currentRatio: number): string {
    const recommendations = {
      [TASK_CATEGORIES.WORK]: {
        low: '工作时间不足，建议增加专注工作的时间',
        normal: '工作时间合理，保持当前节奏',
        high: '工作时间过长，注意劳逸结合'
      },
      [TASK_CATEGORIES.IMPROVEMENT]: {
        low: '学习时间不足，建议每天安排固定的学习时间',
        normal: '学习时间充足，继续保持学习习惯',
        high: '学习时间很充足，可以考虑实践应用'
      },
      [TASK_CATEGORIES.ENTERTAINMENT]: {
        low: '娱乐时间过少，适当放松有助于身心健康',
        normal: '娱乐时间合适，保持工作生活平衡',
        high: '娱乐时间较多，可以考虑更多有意义的活动'
      }
    };

    let level: 'low' | 'normal' | 'high';
    if (currentRatio < 0.2) {
      level = 'low';
    } else if (currentRatio > 0.6) {
      level = 'high';
    } else {
      level = 'normal';
    }

    return recommendations[category][level];
  }

  /**
   * 预测下周建议
   */
  predictNextWeekRecommendations(currentStats: WeeklyStats): string[] {
    const recommendations: string[] = [];

    // 基于当前趋势预测
    if (currentStats.trend === 'improving') {
      recommendations.push('本周平衡状况在改善，继续保持当前的时间安排');
    } else if (currentStats.trend === 'declining') {
      recommendations.push('本周平衡状况在下降，建议调整时间分配策略');
    } else {
      recommendations.push('本周时间分配稳定，可以尝试优化某些细节');
    }

    // 基于平均时间给出建议
    const { averageDaily } = currentStats;
    const total = averageDaily.work + averageDaily.improvement + averageDaily.entertainment;
    
    if (total < 480) { // 少于8小时
      recommendations.push('每日活跃时间较少，建议增加有意义的活动');
    } else if (total > 720) { // 超过12小时
      recommendations.push('每日活跃时间较长，注意适当休息');
    }

    return recommendations;
  }

  /**
   * 获取平衡分数等级描述
   */
  getBalanceScoreDescription(score: number): string {
    if (score >= 90) {
      return '优秀 - 时间分配非常均衡';
    } else if (score >= 80) {
      return '良好 - 时间分配基本均衡';
    } else if (score >= 70) {
      return '一般 - 时间分配需要小幅调整';
    } else if (score >= 60) {
      return '待改善 - 时间分配需要调整';
    } else {
      return '需要改进 - 时间分配严重失衡';
    }
  }

  /**
   * 计算理想的时间分配建议
   */
  calculateIdealTimeAllocation(availableHours: number): CategoryRatios {
    const totalMinutes = availableHours * 60;
    
    return {
      work: Math.round(totalMinutes * 0.6), // 60%
      improvement: Math.round(totalMinutes * 0.25), // 25%
      entertainment: Math.round(totalMinutes * 0.15) // 15%
    };
  }
}
