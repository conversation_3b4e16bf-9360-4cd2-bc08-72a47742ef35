/**
 * 用户配置服务
 * 提供用户配置管理的核心业务逻辑
 */

import {
  UserProfile,
  UserTimeConfig,
  ApiResponse,
  createAppError,
  ERROR_CODES
} from '@/shared';

import {
  CreateUserProfileRequest,
  UpdateUserProfileRequest,
  UserPreferences,
  UserStats,
  OnboardingState,
  validateTimeConfig,
  getDefaultTimeConfig,
  getDefaultUserPreferences
} from '../models/UserProfile';

import { UserConfigRepository } from '../repositories/UserConfigRepository';
import { OnboardingService } from './OnboardingService';

export class UserConfigService {
  private repository: UserConfigRepository;
  private onboardingService: OnboardingService;

  constructor() {
    this.repository = new UserConfigRepository();
    this.onboardingService = new OnboardingService();
  }

  // ============================================================================
  // 用户配置管理
  // ============================================================================

  /**
   * 获取用户配置
   */
  async getUserProfile(userId: string): Promise<ApiResponse<UserProfile | null>> {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: null
      };
    }

    return await this.repository.findById(userId);
  }

  /**
   * 创建用户配置
   */
  async createUserProfile(profileData: CreateUserProfileRequest): Promise<ApiResponse<UserProfile>> {
    // 验证必填字段
    if (!profileData.id || !profileData.email) {
      return {
        success: false,
        error: '用户ID和邮箱不能为空',
        data: null as any
      };
    }

    // 验证时间配置
    if (profileData.timeConfig) {
      const validation = validateTimeConfig(profileData.timeConfig);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join('; '),
          data: null as any
        };
      }
    }

    // 设置默认值
    const completeProfileData: CreateUserProfileRequest = {
      ...profileData,
      timezone: profileData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      timeConfig: profileData.timeConfig || getDefaultTimeConfig(),
      onboardingCompleted: profileData.onboardingCompleted || false
    };

    return await this.repository.create(completeProfileData);
  }

  /**
   * 更新用户配置
   */
  async updateUserProfile(updateData: UpdateUserProfileRequest): Promise<ApiResponse<UserProfile>> {
    // 验证时间配置
    if (updateData.timeConfig) {
      const validation = validateTimeConfig(updateData.timeConfig);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join('; '),
          data: null as any
        };
      }
    }

    return await this.repository.update(updateData);
  }

  /**
   * 保存或更新用户配置
   */
  async saveUserProfile(profileData: CreateUserProfileRequest): Promise<ApiResponse<UserProfile>> {
    // 验证必填字段
    if (!profileData.id || !profileData.email) {
      return {
        success: false,
        error: '用户ID和邮箱不能为空',
        data: null as any
      };
    }

    // 验证时间配置
    if (profileData.timeConfig) {
      const validation = validateTimeConfig(profileData.timeConfig);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join('; '),
          data: null as any
        };
      }
    }

    // 设置默认值
    const completeProfileData: CreateUserProfileRequest = {
      ...profileData,
      timezone: profileData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      timeConfig: profileData.timeConfig || getDefaultTimeConfig(),
      onboardingCompleted: profileData.onboardingCompleted || false
    };

    return await this.repository.upsert(completeProfileData);
  }

  /**
   * 删除用户配置
   */
  async deleteUserProfile(userId: string): Promise<ApiResponse<void>> {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: undefined
      };
    }

    return await this.repository.delete(userId);
  }

  // ============================================================================
  // 引导流程管理
  // ============================================================================

  /**
   * 检查用户是否完成引导
   */
  async isOnboardingCompleted(userId: string): Promise<ApiResponse<boolean>> {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: false
      };
    }

    return await this.repository.isOnboardingCompleted(userId);
  }

  /**
   * 初始化引导流程
   */
  initializeOnboarding(): OnboardingState {
    return this.onboardingService.initializeOnboardingState();
  }

  /**
   * 验证引导步骤
   */
  validateOnboardingStep(state: OnboardingState): ApiResponse<boolean> {
    return this.onboardingService.validateCurrentStep(state);
  }

  /**
   * 移动到下一个引导步骤
   */
  moveToNextOnboardingStep(state: OnboardingState): ApiResponse<OnboardingState> {
    return this.onboardingService.moveToNextStep(state);
  }

  /**
   * 移动到上一个引导步骤
   */
  moveToPreviousOnboardingStep(state: OnboardingState): ApiResponse<OnboardingState> {
    return this.onboardingService.moveToPreviousStep(state);
  }

  /**
   * 跳转到指定引导步骤
   */
  jumpToOnboardingStep(state: OnboardingState, stepIndex: number): ApiResponse<OnboardingState> {
    return this.onboardingService.jumpToStep(state, stepIndex);
  }

  /**
   * 更新引导时间配置
   */
  updateOnboardingTimeConfig(state: OnboardingState, updates: Partial<UserTimeConfig>): OnboardingState {
    return this.onboardingService.updateTimeConfig(state, updates);
  }

  /**
   * 完成引导流程
   */
  async completeOnboarding(userId: string, email: string, state: OnboardingState): Promise<ApiResponse<UserProfile>> {
    // 验证引导完成
    const completionResult = this.onboardingService.completeOnboarding(state);
    if (!completionResult.success) {
      return {
        success: false,
        error: completionResult.error,
        data: null as any
      };
    }

    // 保存用户配置
    const profileData: CreateUserProfileRequest = {
      id: userId,
      email,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timeConfig: completionResult.data,
      onboardingCompleted: true
    };

    return await this.saveUserProfile(profileData);
  }

  // ============================================================================
  // 用户偏好管理
  // ============================================================================

  /**
   * 获取用户偏好设置
   */
  async getUserPreferences(userId: string): Promise<ApiResponse<UserPreferences>> {
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: null as any
      };
    }

    return await this.repository.getUserPreferences(userId);
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<ApiResponse<UserPreferences>> {
    if (!preferences.userId) {
      return {
        success: false,
        error: '用户ID不能为空',
        data: null as any
      };
    }

    // 验证偏好设置
    const validation = this.validateUserPreferences(preferences);
    if (!validation.success) {
      return {
        success: false,
        error: validation.error,
        data: null as any
      };
    }

    return await this.repository.updateUserPreferences(preferences);
  }

  /**
   * 验证用户偏好设置
   */
  private validateUserPreferences(preferences: Partial<UserPreferences>): ApiResponse<boolean> {
    const errors: string[] = [];

    if (preferences.maxContinuousWork !== undefined) {
      if (preferences.maxContinuousWork < 30 || preferences.maxContinuousWork > 240) {
        errors.push('最大连续工作时间应在30-240分钟之间');
      }
    }

    if (preferences.breakInterval !== undefined) {
      if (preferences.breakInterval < 5 || preferences.breakInterval > 60) {
        errors.push('休息间隔应在5-60分钟之间');
      }
    }

    if (preferences.notificationSettings) {
      const { reminderMinutes } = preferences.notificationSettings;
      if (reminderMinutes) {
        for (const minutes of reminderMinutes) {
          if (minutes < 0 || minutes > 10080) { // 最多一周
            errors.push('提醒时间应在0-10080分钟之间');
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      error: errors.join('; '),
      data: errors.length === 0
    };
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 获取用户时区
   */
  async getUserTimezone(userId: string): Promise<ApiResponse<string>> {
    const profileResult = await this.getUserProfile(userId);
    if (!profileResult.success || !profileResult.data) {
      return {
        success: true,
        data: 'UTC' // 默认时区
      };
    }

    return {
      success: true,
      data: profileResult.data.timezone
    };
  }

  /**
   * 获取用户时间配置
   */
  async getUserTimeConfig(userId: string): Promise<ApiResponse<UserTimeConfig>> {
    const profileResult = await this.getUserProfile(userId);
    if (!profileResult.success || !profileResult.data) {
      return {
        success: true,
        data: getDefaultTimeConfig()
      };
    }

    return {
      success: true,
      data: profileResult.data.timeConfig
    };
  }

  /**
   * 重置用户配置为默认值
   */
  async resetUserConfigToDefaults(userId: string): Promise<ApiResponse<UserProfile>> {
    const profileResult = await this.getUserProfile(userId);
    if (!profileResult.success || !profileResult.data) {
      return {
        success: false,
        error: '用户配置不存在',
        data: null as any
      };
    }

    const updateData: UpdateUserProfileRequest = {
      id: userId,
      timeConfig: getDefaultTimeConfig()
    };

    return await this.updateUserProfile(updateData);
  }

  /**
   * 导出用户配置
   */
  async exportUserConfig(userId: string): Promise<ApiResponse<any>> {
    const [profileResult, preferencesResult] = await Promise.all([
      this.getUserProfile(userId),
      this.getUserPreferences(userId)
    ]);

    if (!profileResult.success) {
      return {
        success: false,
        error: profileResult.error,
        data: null
      };
    }

    const exportData = {
      profile: profileResult.data,
      preferences: preferencesResult.success ? preferencesResult.data : null,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    return {
      success: true,
      data: exportData
    };
  }

  /**
   * 导入用户配置
   */
  async importUserConfig(userId: string, configData: any): Promise<ApiResponse<UserProfile>> {
    try {
      // 验证导入数据格式
      if (!configData.profile) {
        return {
          success: false,
          error: '导入数据格式不正确',
          data: null as any
        };
      }

      // 更新用户配置
      const updateData: UpdateUserProfileRequest = {
        id: userId,
        timeConfig: configData.profile.timeConfig,
        timezone: configData.profile.timezone
      };

      const profileResult = await this.updateUserProfile(updateData);
      if (!profileResult.success) {
        return profileResult;
      }

      // 更新用户偏好（如果存在）
      if (configData.preferences) {
        await this.updateUserPreferences({
          ...configData.preferences,
          userId
        });
      }

      return profileResult;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '导入配置时发生未知错误',
        data: null as any
      };
    }
  }
}
