# TimeManager 更新技术规范

## 新的数据模型

### 核心任务表
```typescript
interface Task {
  id: string;
  title: string;
  description?: string;
  
  // 优先级维度
  importance: 1 | 2 | 3 | 4 | 5;
  urgency: 1 | 2 | 3 | 4 | 5;
  
  // 时间维度
  deadline: Date;
  isLongTerm: boolean;
  frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  estimatedDuration: number; // 分钟
  
  // 分类维度
  category: 'work' | 'improvement' | 'entertainment';
  
  // 状态信息
  status: 'pending' | 'in-progress' | 'completed' | 'postponed';
  postponeCount: number;
  
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}
```

### 任务历史表
```typescript
interface TaskHistory {
  id: string;
  taskId: string;
  action: 'created' | 'started' | 'completed' | 'postponed' | 'cancelled';
  timestamp: Date;
  originalDeadline?: Date;
  newDeadline?: Date;
  reason?: string;
  metadata?: Record<string, any>;
}
```

### 用户行为分析表
```typescript
interface UserBehaviorMetrics {
  id: string;
  userId: string;
  date: Date;
  
  // 完成情况统计
  workTasksCompleted: number;
  improvementTasksCompleted: number;
  entertainmentTasksCompleted: number;
  
  // 时间分配统计
  workTimeSpent: number; // 分钟
  improvementTimeSpent: number;
  entertainmentTimeSpent: number;
  
  // 推迟情况统计
  tasksPostponed: number;
  averagePostponeReason: string;
  
  // 平衡分数
  balanceScore: number;
}
```

## 三大算法实现

### 1. 规划算法（Planning Algorithm）
```typescript
interface PlanningConfig {
  workingHours: { start: string; end: string };
  breakDuration: number;
  maxContinuousWorkTime: number;
  categoryTimeRatios: {
    work: number;
    improvement: number;
    entertainment: number;
  };
}

class PlanningAlgorithm {
  generateDailySchedule(tasks: Task[], config: PlanningConfig): TimeSlot[] {
    // 1. 按综合分数排序任务
    const scoredTasks = this.calculateTaskScores(tasks);
    
    // 2. 应用四象限分类
    const quadrants = this.classifyByQuadrant(scoredTasks);
    
    // 3. 考虑时间约束生成安排
    return this.scheduleWithConstraints(quadrants, config);
  }
  
  private calculateTaskScores(tasks: Task[]): ScoredTask[] {
    return tasks.map(task => ({
      ...task,
      score: this.calculateScore(task),
      quadrant: this.determineQuadrant(task.importance, task.urgency)
    }));
  }
  
  private calculateScore(task: Task): number {
    const baseScore = task.importance * 0.6 + task.urgency * 0.4;
    const categoryWeight = this.getCategoryWeight(task.category);
    const postponePenalty = task.postponeCount * 5; // 推迟惩罚
    
    return baseScore + categoryWeight + postponePenalty;
  }
}
```

### 2. LifeBalance算法
```typescript
class LifeBalanceAlgorithm {
  private readonly IDEAL_RATIOS = {
    work: 0.6,
    improvement: 0.25,
    entertainment: 0.15
  };
  
  analyzeBalance(userId: string, days: number = 7): BalanceAnalysis {
    const metrics = this.getUserMetrics(userId, days);
    const currentRatios = this.calculateCurrentRatios(metrics);
    const balanceScore = this.calculateBalanceScore(currentRatios);
    
    return {
      currentRatios,
      idealRatios: this.IDEAL_RATIOS,
      balanceScore,
      recommendations: this.generateRecommendations(currentRatios),
      needsIntervention: balanceScore < 0.7
    };
  }
  
  private calculateBalanceScore(ratios: CategoryRatios): number {
    let score = 1.0;
    
    Object.keys(this.IDEAL_RATIOS).forEach(category => {
      const ideal = this.IDEAL_RATIOS[category];
      const actual = ratios[category];
      const deviation = Math.abs(ideal - actual);
      score -= deviation * 0.5; // 偏差惩罚
    });
    
    return Math.max(0, score);
  }
  
  generateRecommendations(ratios: CategoryRatios): string[] {
    const recommendations = [];
    
    if (ratios.work > 0.8) {
      recommendations.push("工作时间过长，建议增加休息和娱乐时间");
    }
    
    if (ratios.improvement < 0.1) {
      recommendations.push("建议安排一些自我提升的活动");
    }
    
    if (ratios.entertainment < 0.05) {
      recommendations.push("需要更多的放松和娱乐时间");
    }
    
    return recommendations;
  }
}
```

### 3. Fix算法
```typescript
class FixAlgorithm {
  private readonly POSTPONE_THRESHOLDS = {
    low: 2,      // 推迟2次以上
    medium: 4,   // 推迟4次以上
    high: 6,     // 推迟6次以上
    critical: 8  // 推迟8次以上
  };
  
  analyzePostponedTasks(userId: string): PostponedTaskAlert[] {
    const postponedTasks = this.getPostponedTasks(userId);
    
    return postponedTasks.map(task => {
      const urgencyLevel = this.calculateUrgencyLevel(task);
      const rootCause = this.analyzePostponeReason(task);
      const suggestion = this.generateFixSuggestion(task, rootCause);
      
      return {
        task,
        postponeCount: task.postponeCount,
        daysSinceFirstPostpone: this.calculateDaysSinceFirstPostpone(task),
        urgencyLevel,
        rootCause,
        suggestion,
        shouldAlert: urgencyLevel !== 'low'
      };
    });
  }
  
  private analyzePostponeReason(task: Task): PostponeReason {
    const history = this.getTaskHistory(task.id);
    
    // 分析推迟模式
    const postponeReasons = history
      .filter(h => h.action === 'postponed')
      .map(h => h.reason);
    
    // 使用简单的规则引擎分析
    if (postponeReasons.includes('emergency')) {
      return 'emergency_interference';
    } else if (postponeReasons.includes('lack_time')) {
      return 'time_management_issue';
    } else if (postponeReasons.includes('low_motivation')) {
      return 'motivation_issue';
    } else {
      return 'unknown';
    }
  }
  
  private generateFixSuggestion(task: Task, reason: PostponeReason): string {
    switch (reason) {
      case 'emergency_interference':
        return "建议将此任务分解为更小的部分，或设置更灵活的时间安排";
      case 'time_management_issue':
        return "建议重新评估任务的时间预估，或调整其他任务的优先级";
      case 'motivation_issue':
        return "建议设置奖励机制，或寻找任务的内在动机";
      default:
        return "建议重新评估任务的重要性和紧急性";
    }
  }
}
```

## 算法协调机制

```typescript
class AlgorithmCoordinator {
  generateOptimalSchedule(userId: string, date: Date): OptimalSchedule {
    // 1. 获取基础规划
    const basicSchedule = this.planningAlgorithm.generateDailySchedule(
      this.getTasks(userId, date),
      this.getUserConfig(userId)
    );
    
    // 2. 应用生活平衡调整
    const balanceAnalysis = this.lifeBalanceAlgorithm.analyzeBalance(userId);
    const balanceAdjustedSchedule = this.applyBalanceAdjustments(
      basicSchedule, 
      balanceAnalysis
    );
    
    // 3. 应用Fix算法建议
    const postponedAlerts = this.fixAlgorithm.analyzePostponedTasks(userId);
    const finalSchedule = this.applyFixSuggestions(
      balanceAdjustedSchedule,
      postponedAlerts
    );
    
    return {
      schedule: finalSchedule,
      balanceInsights: balanceAnalysis,
      postponedTaskAlerts: postponedAlerts.filter(alert => alert.shouldAlert),
      overallRecommendations: this.generateOverallRecommendations()
    };
  }
}
```

## 实现建议

### 开发优先级
1. **Phase 1**：基础规划算法 + 新数据模型
2. **Phase 2**：Fix算法（相对简单，用户价值明显）
3. **Phase 3**：LifeBalance算法（最复杂，需要更多数据）

### 关键成功因素
- 算法参数需要A/B测试优化
- 需要用户反馈机制来改进算法
- 考虑用户隐私保护
- 设计良好的解释性界面

这个设计在技术上是可行的，创新性很强！你觉得这个实现方案如何？
