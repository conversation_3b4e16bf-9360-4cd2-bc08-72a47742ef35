# TimeManager MVP 设计方案

## 🎯 MVP核心目标

**用户价值**：用户输入任务后，系统自动生成智能时间规划，并通过日历和桌面提醒确保执行

**技术目标**：验证三大算法的有效性，建立可扩展的技术架构

## 🔧 MVP功能范围

### 1. 时间规划算法（简化版）
```typescript
// MVP版本的规划算法
interface SimplePlanningAlgorithm {
  // 输入：今日任务列表
  input: Task[];
  
  // 处理：四象限分类 + 简单排序
  process: {
    quadrantClassification: (task: Task) => 1 | 2 | 3 | 4;
    priorityScoring: (task: Task) => number;
    timeSlotGeneration: (tasks: Task[]) => TimeSlot[];
  };
  
  // 输出：今日时间安排
  output: DailySchedule;
}

// 简化的评分公式
function calculateMVPScore(task: Task): number {
  const baseScore = task.importance * 0.6 + task.urgency * 0.4;
  const categoryBonus = {
    work: 0,
    improvement: 2,
    entertainment: 1
  }[task.category];
  const postponePenalty = task.postponeCount * 3;
  
  return baseScore + categoryBonus + postponePenalty;
}
```

### 2. 生活平衡机制（简化版）
```typescript
// MVP版本的平衡算法
interface SimpleBalanceAlgorithm {
  // 分析最近7天的任务完成情况
  analyzeWeeklyBalance(userId: string): {
    workRatio: number;      // 工作任务时间占比
    improvementRatio: number; // 提升任务时间占比
    entertainmentRatio: number; // 娱乐任务时间占比
    balanceScore: number;   // 0-100分
    recommendation: string; // 简单建议
  };
  
  // 简化的平衡评分
  calculateBalanceScore(ratios: CategoryRatios): number {
    const ideal = { work: 0.6, improvement: 0.25, entertainment: 0.15 };
    let score = 100;
    
    Object.keys(ideal).forEach(category => {
      const deviation = Math.abs(ideal[category] - ratios[category]);
      score -= deviation * 100; // 偏差惩罚
    });
    
    return Math.max(0, score);
  }
}
```

### 3. 修复机制（简化版）
```typescript
// MVP版本的修复算法
interface SimpleFixAlgorithm {
  // 检测需要修复的任务
  detectProblematicTasks(userId: string): {
    taskId: string;
    postponeCount: number;
    daysSinceCreated: number;
    urgencyLevel: 'medium' | 'high' | 'critical';
    suggestion: string;
  }[];
  
  // 简化的修复建议
  generateFixSuggestion(task: Task): string {
    if (task.postponeCount >= 5) {
      return "这个任务已经推迟5次了，建议分解为更小的子任务";
    } else if (task.postponeCount >= 3) {
      return "建议调整任务的时间安排或降低难度";
    } else {
      return "建议设置提醒，确保按时完成";
    }
  }
}
```

## 🏗️ MVP技术架构

### 前端架构
```
Web应用 (Next.js 14)
├── 任务管理页面
├── 今日规划页面  
├── 设置页面
└── 统计页面

桌面悬浮窗 (Tauri)
├── 迷你任务显示
├── 快速添加任务
├── 今日进度显示
└── 一键同步
```

### 后端架构
```
Supabase后端
├── PostgreSQL数据库
├── 实时订阅
├── 用户认证
├── Edge Functions (算法计算)
└── 文件存储
```

### 数据模型（MVP简化版）
```sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR,
  timezone VARCHAR DEFAULT 'UTC',
  created_at TIMESTAMP DEFAULT NOW()
);

-- 任务表
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  title VARCHAR NOT NULL,
  category VARCHAR CHECK (category IN ('work', 'improvement', 'entertainment')),
  importance INTEGER CHECK (importance BETWEEN 1 AND 5),
  urgency INTEGER CHECK (urgency BETWEEN 1 AND 5),
  deadline TIMESTAMP,
  estimated_duration INTEGER, -- 分钟
  status VARCHAR DEFAULT 'pending',
  postpone_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 任务完成记录表
CREATE TABLE task_completions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES tasks(id),
  completed_at TIMESTAMP DEFAULT NOW(),
  actual_duration INTEGER, -- 实际花费时间
  satisfaction_score INTEGER CHECK (satisfaction_score BETWEEN 1 AND 5)
);

-- 每日统计表
CREATE TABLE daily_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  date DATE NOT NULL,
  work_time INTEGER DEFAULT 0,
  improvement_time INTEGER DEFAULT 0,
  entertainment_time INTEGER DEFAULT 0,
  tasks_completed INTEGER DEFAULT 0,
  tasks_postponed INTEGER DEFAULT 0,
  balance_score INTEGER,
  UNIQUE(user_id, date)
);
```

## 📱 推送机制设计

### 1. APP端 - 日历集成
```typescript
// 日历事件创建
interface CalendarIntegration {
  // 创建日历事件
  createCalendarEvent(task: Task, timeSlot: TimeSlot): {
    title: string;
    description: string;
    startTime: Date;
    endTime: Date;
    reminder: number[]; // 提前提醒时间（分钟）
    category: string;
  };
  
  // 同步到系统日历
  syncToSystemCalendar(events: CalendarEvent[]): Promise<void>;
}

// 移动端实现
class MobileCalendarSync {
  async syncDailySchedule(schedule: DailySchedule) {
    const events = schedule.timeSlots.map(slot => ({
      title: `📋 ${slot.task.title}`,
      description: `分类: ${slot.task.category}\n重要性: ${slot.task.importance}/5`,
      startTime: slot.startTime,
      endTime: slot.endTime,
      reminder: [15, 5], // 提前15分钟和5分钟提醒
      category: slot.task.category
    }));
    
    await this.createCalendarEvents(events);
  }
}
```

### 2. PC端 - 桌面悬浮窗
```typescript
// Tauri桌面应用
interface DesktopFloatingWidget {
  // 悬浮窗配置
  config: {
    width: 300;
    height: 400;
    alwaysOnTop: true;
    resizable: false;
    position: 'top-right';
  };
  
  // 显示内容
  content: {
    currentTask: Task | null;
    nextTasks: Task[];
    todayProgress: {
      completed: number;
      total: number;
      timeSpent: number;
    };
    balanceIndicator: {
      score: number;
      status: 'good' | 'warning' | 'poor';
    };
  };
}

// 桌面通知
class DesktopNotifications {
  // 任务提醒
  async notifyTaskStart(task: Task) {
    await invoke('show_notification', {
      title: '⏰ 任务提醒',
      body: `该开始 "${task.title}" 了`,
      icon: 'task-icon.png'
    });
  }
  
  // 平衡提醒
  async notifyBalanceWarning(message: string) {
    await invoke('show_notification', {
      title: '⚖️ 生活平衡提醒',
      body: message,
      icon: 'balance-icon.png'
    });
  }
}
```

### 3. 日历助手推荐
```typescript
// 智能推荐系统
class CalendarAssistant {
  // 分析日历空闲时间
  analyzeAvailableSlots(calendar: CalendarEvent[]): TimeSlot[] {
    // 找出空闲时间段
    const freeSlots = this.findFreeTimeSlots(calendar);
    
    // 过滤出适合工作的时间段
    return freeSlots.filter(slot => 
      slot.duration >= 30 && // 至少30分钟
      this.isProductiveTime(slot.startTime) // 高效时间段
    );
  }
  
  // 推荐任务安排
  recommendTaskScheduling(tasks: Task[], availableSlots: TimeSlot[]): Recommendation[] {
    return tasks.map(task => {
      const bestSlot = this.findBestSlotForTask(task, availableSlots);
      return {
        task,
        recommendedSlot: bestSlot,
        reason: this.explainRecommendation(task, bestSlot),
        confidence: this.calculateConfidence(task, bestSlot)
      };
    });
  }
}
```

## 🚀 MVP开发计划（6周）

### Week 1-2: 基础架构
```
□ Supabase项目设置
□ Next.js项目初始化
□ 基础UI组件库
□ 用户认证系统
□ 任务CRUD功能
□ 基础数据模型
```

### Week 3-4: 核心算法
```
□ 时间规划算法实现
□ 生活平衡算法实现
□ 修复算法实现
□ 算法测试和调优
□ 今日规划页面
□ 统计分析页面
```

### Week 5-6: 推送集成
```
□ Tauri桌面应用开发
□ 桌面悬浮窗实现
□ 日历API集成
□ 推送通知系统
□ 用户测试和反馈
□ 性能优化
```

## 💰 MVP成本预算

```
开发阶段 (6周): $0
├── Supabase免费层: 500MB + 50K MAU
├── Vercel免费层: 100GB带宽
├── Google Calendar API: 免费配额
└── 开发工具: 全部免费

运营阶段 (前3个月): $0-25/月
├── 用户 < 1000: 继续免费层
├── 用户 > 1000: 升级Supabase Pro ($25/月)
└── 域名费用: $10-15/年
```

## 🎯 MVP成功指标

### 用户指标
- 日活用户 > 100
- 用户留存率 > 60% (7天)
- 任务完成率 > 70%

### 功能指标  
- 平均规划生成时间 < 2秒
- 算法推荐准确率 > 80%
- 用户满意度 > 4.0/5.0

### 技术指标
- 页面加载时间 < 3秒
- API响应时间 < 500ms
- 系统可用性 > 99%

这个MVP设计保持了核心功能的完整性，同时控制了开发复杂度。你觉得这个方案如何？我们可以开始实际的开发工作吗？
