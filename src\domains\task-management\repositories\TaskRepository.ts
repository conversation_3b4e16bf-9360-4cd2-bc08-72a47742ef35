/**
 * 任务仓储层
 * 负责任务数据的持久化操作
 */

import { supabase } from '@/lib/supabase';
import {
  Task,
  TaskCategory,
  TaskStatus,
  ApiResponse,
  createAppError,
  ERROR_CODES
} from '@/shared';

import {
  TaskRecord,
  CreateTaskRequest,
  UpdateTaskRequest,
  TaskFilter,
  TaskSortConfig,
  TaskPaginationOptions,
  TaskPaginationResult,
  mapRecordToTask,
  mapTaskToRecord
} from '../models/Task';

export class TaskRepository {

  /**
   * 获取用户的所有任务
   */
  async findByUserId(userId: string): Promise<ApiResponse<Task[]>> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        return {
          success: false,
          error: `获取任务失败: ${error.message}`,
          data: []
        };
      }

      const tasks = data.map(mapRecordToTask);

      return {
        success: true,
        data: tasks
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取任务时发生未知错误',
        data: []
      };
    }
  }

  /**
   * 根据ID获取任务
   */
  async findById(id: string): Promise<ApiResponse<Task | null>> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return {
            success: true,
            data: null
          };
        }
        return {
          success: false,
          error: `获取任务失败: ${error.message}`,
          data: null
        };
      }

      const task = mapRecordToTask(data);

      return {
        success: true,
        data: task
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取任务时发生未知错误',
        data: null
      };
    }
  }

  /**
   * 创建新任务
   */
  async create(taskData: CreateTaskRequest): Promise<ApiResponse<Task>> {
    try {
      const record = mapTaskToRecord(taskData);

      const { data, error } = await supabase
        .from('tasks')
        .insert(record)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: `创建任务失败: ${error.message}`,
          data: null as any
        };
      }

      const task = mapRecordToTask(data);

      return {
        success: true,
        data: task
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建任务时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 更新任务
   */
  async update(updateData: UpdateTaskRequest): Promise<ApiResponse<Task>> {
    try {
      const { id, ...updates } = updateData;
      
      // 构建更新数据
      const updateRecord: Partial<TaskRecord> = {};
      
      if (updates.title !== undefined) updateRecord.title = updates.title;
      if (updates.description !== undefined) updateRecord.description = updates.description;
      if (updates.category !== undefined) updateRecord.category = updates.category;
      if (updates.importance !== undefined) updateRecord.importance = updates.importance;
      if (updates.urgency !== undefined) updateRecord.urgency = updates.urgency;
      if (updates.deadline !== undefined) updateRecord.deadline = updates.deadline.toISOString();
      if (updates.estimatedDuration !== undefined) updateRecord.estimated_duration = updates.estimatedDuration;
      if (updates.status !== undefined) updateRecord.status = updates.status;
      if (updates.postponeCount !== undefined) updateRecord.postpone_count = updates.postponeCount;

      const { data, error } = await supabase
        .from('tasks')
        .update(updateRecord)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error: `更新任务失败: ${error.message}`,
          data: null as any
        };
      }

      const task = mapRecordToTask(data);

      return {
        success: true,
        data: task
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新任务时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 删除任务
   */
  async delete(id: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', id);

      if (error) {
        return {
          success: false,
          error: `删除任务失败: ${error.message}`,
          data: undefined
        };
      }

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除任务时发生未知错误',
        data: undefined
      };
    }
  }

  /**
   * 根据条件过滤任务
   */
  async findByFilter(filter: TaskFilter): Promise<ApiResponse<Task[]>> {
    try {
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('user_id', filter.userId);

      // 应用状态过滤
      if (filter.status) {
        if (Array.isArray(filter.status)) {
          query = query.in('status', filter.status);
        } else {
          query = query.eq('status', filter.status);
        }
      }

      // 应用分类过滤
      if (filter.category) {
        if (Array.isArray(filter.category)) {
          query = query.in('category', filter.category);
        } else {
          query = query.eq('category', filter.category);
        }
      }

      // 应用日期范围过滤
      if (filter.startDate) {
        query = query.gte('deadline', filter.startDate.toISOString());
      }
      if (filter.endDate) {
        query = query.lte('deadline', filter.endDate.toISOString());
      }

      // 应用搜索条件
      if (filter.searchTerm) {
        query = query.or(`title.ilike.%${filter.searchTerm}%,description.ilike.%${filter.searchTerm}%`);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        return {
          success: false,
          error: `过滤任务失败: ${error.message}`,
          data: []
        };
      }

      const tasks = data.map(mapRecordToTask);

      return {
        success: true,
        data: tasks
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '过滤任务时发生未知错误',
        data: []
      };
    }
  }

  /**
   * 分页获取任务
   */
  async findWithPagination(
    userId: string,
    pagination: TaskPaginationOptions,
    sort?: TaskSortConfig
  ): Promise<ApiResponse<TaskPaginationResult>> {
    try {
      const { page, pageSize } = pagination;
      const offset = (page - 1) * pageSize;

      let query = supabase
        .from('tasks')
        .select('*', { count: 'exact' })
        .eq('user_id', userId);

      // 应用排序
      if (sort) {
        const column = this.mapSortOptionToColumn(sort.sortBy);
        query = query.order(column, { ascending: sort.direction === 'asc' });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // 应用分页
      query = query.range(offset, offset + pageSize - 1);

      const { data, error, count } = await query;

      if (error) {
        return {
          success: false,
          error: `分页获取任务失败: ${error.message}`,
          data: null as any
        };
      }

      const tasks = data.map(mapRecordToTask);
      const total = count || 0;
      const totalPages = Math.ceil(total / pageSize);

      return {
        success: true,
        data: {
          tasks,
          total,
          page,
          pageSize,
          totalPages
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '分页获取任务时发生未知错误',
        data: null as any
      };
    }
  }

  /**
   * 批量更新任务状态
   */
  async batchUpdateStatus(taskIds: string[], status: TaskStatus): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ status })
        .in('id', taskIds);

      if (error) {
        return {
          success: false,
          error: `批量更新任务状态失败: ${error.message}`,
          data: undefined
        };
      }

      return {
        success: true,
        data: undefined
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量更新任务状态时发生未知错误',
        data: undefined
      };
    }
  }

  /**
   * 映射排序选项到数据库列名
   */
  private mapSortOptionToColumn(sortOption: string): string {
    const mapping: Record<string, string> = {
      deadline: 'deadline',
      importance: 'importance',
      urgency: 'urgency',
      createdAt: 'created_at',
      estimatedDuration: 'estimated_duration'
    };

    return mapping[sortOption] || 'created_at';
  }
}
