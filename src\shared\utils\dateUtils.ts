/**
 * 日期和时间相关的工具函数
 */

import { TIME_CONSTANTS } from '../constants';

// ============================================================================
// 日期格式化
// ============================================================================

/**
 * 格式化日期为 YYYY-MM-DD 格式
 */
export function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * 格式化时间为 HH:MM 格式
 */
export function formatTime(date: Date): string {
  return date.toTimeString().slice(0, 5);
}

/**
 * 格式化日期时间为 YYYY-MM-DDTHH:MM 格式（用于 datetime-local input）
 */
export function formatDateTime(date: Date): string {
  return date.toISOString().slice(0, 16);
}

/**
 * 格式化持续时间为可读格式
 */
export function formatDuration(minutes: number): string {
  if (minutes < TIME_CONSTANTS.MINUTES_PER_HOUR) {
    return `${minutes}分钟`;
  }
  
  const hours = Math.floor(minutes / TIME_CONSTANTS.MINUTES_PER_HOUR);
  const remainingMinutes = minutes % TIME_CONSTANTS.MINUTES_PER_HOUR;
  
  if (remainingMinutes === 0) {
    return `${hours}小时`;
  }
  
  return `${hours}小时${remainingMinutes}分钟`;
}

// ============================================================================
// 日期计算
// ============================================================================

/**
 * 获取今天的开始时间 (00:00:00)
 */
export function getStartOfDay(date: Date = new Date()): Date {
  const start = new Date(date);
  start.setHours(0, 0, 0, 0);
  return start;
}

/**
 * 获取今天的结束时间 (23:59:59)
 */
export function getEndOfDay(date: Date = new Date()): Date {
  const end = new Date(date);
  end.setHours(23, 59, 59, 999);
  return end;
}

/**
 * 获取明天的日期
 */
export function getTomorrow(date: Date = new Date()): Date {
  const tomorrow = new Date(date);
  tomorrow.setDate(tomorrow.getDate() + 1);
  return tomorrow;
}

/**
 * 获取本周的开始日期（周一）
 */
export function getStartOfWeek(date: Date = new Date()): Date {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
  start.setDate(diff);
  return getStartOfDay(start);
}

/**
 * 获取本周的结束日期（周日）
 */
export function getEndOfWeek(date: Date = new Date()): Date {
  const end = new Date(getStartOfWeek(date));
  end.setDate(end.getDate() + 6);
  return getEndOfDay(end);
}

/**
 * 检查两个日期是否是同一天
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return formatDate(date1) === formatDate(date2);
}

/**
 * 检查日期是否是今天
 */
export function isToday(date: Date): boolean {
  return isSameDay(date, new Date());
}

/**
 * 检查日期是否是明天
 */
export function isTomorrow(date: Date): boolean {
  return isSameDay(date, getTomorrow());
}

/**
 * 计算两个日期之间的天数差
 */
export function getDaysDifference(date1: Date, date2: Date): number {
  const timeDiff = date2.getTime() - date1.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

// ============================================================================
// 时间解析和创建
// ============================================================================

/**
 * 解析时间字符串 (HH:MM) 并创建今天的对应时间
 */
export function parseTimeToday(timeStr: string, baseDate: Date = new Date()): Date {
  const [hours, minutes] = timeStr.split(':').map(Number);
  const date = new Date(baseDate);
  date.setHours(hours, minutes, 0, 0);
  return date;
}

/**
 * 创建指定日期和时间的 Date 对象
 */
export function createDateTime(date: Date, timeStr: string): Date {
  const [hours, minutes] = timeStr.split(':').map(Number);
  const result = new Date(date);
  result.setHours(hours, minutes, 0, 0);
  return result;
}

/**
 * 获取默认的截止时间（明天下午6点）
 */
export function getDefaultDeadline(): Date {
  const tomorrow = getTomorrow();
  tomorrow.setHours(18, 0, 0, 0);
  return tomorrow;
}

// ============================================================================
// 时间范围检查
// ============================================================================

/**
 * 检查时间是否在指定范围内
 */
export function isTimeInRange(
  time: Date, 
  startTime: string, 
  endTime: string, 
  baseDate: Date = new Date()
): boolean {
  const start = parseTimeToday(startTime, baseDate);
  const end = parseTimeToday(endTime, baseDate);
  
  return time >= start && time <= end;
}

/**
 * 检查两个时间段是否重叠
 */
export function isTimeRangeOverlap(
  start1: Date,
  end1: Date,
  start2: Date,
  end2: Date
): boolean {
  return start1 < end2 && end1 > start2;
}

/**
 * 计算两个时间之间的分钟数
 */
export function getMinutesBetween(start: Date, end: Date): number {
  return Math.round((end.getTime() - start.getTime()) / (1000 * 60));
}

// ============================================================================
// 工作日相关
// ============================================================================

/**
 * 检查日期是否是工作日
 */
export function isWorkday(date: Date, workDays: string[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']): boolean {
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const dayName = dayNames[date.getDay()];
  return workDays.includes(dayName);
}

/**
 * 获取下一个工作日
 */
export function getNextWorkday(date: Date = new Date(), workDays: string[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']): Date {
  let nextDay = new Date(date);
  nextDay.setDate(nextDay.getDate() + 1);
  
  while (!isWorkday(nextDay, workDays)) {
    nextDay.setDate(nextDay.getDate() + 1);
  }
  
  return nextDay;
}

// ============================================================================
// 相对时间
// ============================================================================

/**
 * 获取相对时间描述
 */
export function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '明天';
  } else if (diffDays === -1) {
    return '昨天';
  } else if (diffDays > 1 && diffDays <= 7) {
    return `${diffDays}天后`;
  } else if (diffDays < -1 && diffDays >= -7) {
    return `${Math.abs(diffDays)}天前`;
  } else {
    return formatDate(date);
  }
}
