'use client';

import { useState } from 'react';
import { X, Clock, AlertTriangle, CheckCircle, ArrowRight, Settings } from 'lucide-react';
import { AdjustmentResult, AdjustmentStrategy } from '@/lib/algorithms/timeAdjustment';

interface TimeAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  adjustmentResult: AdjustmentResult | null;
  strategies: AdjustmentStrategy[];
  currentStrategy: string;
  onStrategyChange: (strategy: string) => void;
  onApplyAdjustment: () => void;
  onRejectAdjustment: () => void;
}

export default function TimeAdjustmentModal({
  isOpen,
  onClose,
  adjustmentResult,
  strategies,
  currentStrategy,
  onStrategyChange,
  onApplyAdjustment,
  onRejectAdjustment
}: TimeAdjustmentModalProps) {
  const [selectedStrategy, setSelectedStrategy] = useState(currentStrategy);

  if (!isOpen || !adjustmentResult) return null;

  const getImpactColor = (score: number) => {
    if (score <= 20) return 'text-green-600 bg-green-50';
    if (score <= 40) return 'text-yellow-600 bg-yellow-50';
    if (score <= 60) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  const getImpactText = (score: number) => {
    if (score <= 20) return '影响很小';
    if (score <= 40) return '影响适中';
    if (score <= 60) return '影响较大';
    return '影响很大';
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'postpone': return '⏰';
      case 'compress': return '🗜️';
      case 'move': return '📍';
      case 'split': return '✂️';
      case 'useRestTime': return '☕';
      case 'extendWorkHours': return '🌙';
      default: return '📝';
    }
  };

  const getActionDescription = (type: string) => {
    switch (type) {
      case 'postpone': return '推迟';
      case 'compress': return '压缩';
      case 'move': return '移动';
      case 'split': return '分割';
      case 'useRestTime': return '占用休息时间';
      case 'extendWorkHours': return '延长工作时间';
      default: return '调整';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center">
            <Clock className="h-6 w-6 text-indigo-600 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">时间调整建议</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* 调整策略选择 */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <Settings className="h-5 w-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">调整策略</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {strategies.map((strategy) => (
                <button
                  key={strategy.name}
                  onClick={() => {
                    setSelectedStrategy(strategy.name);
                    onStrategyChange(strategy.name);
                  }}
                  className={`p-3 rounded-lg border text-left transition-colors ${
                    selectedStrategy === strategy.name
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium text-gray-900">{strategy.name}</div>
                  <div className="text-sm text-gray-600 mt-1">{strategy.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* 调整结果概览 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">调整结果概览</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center">
                  {adjustmentResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                  )}
                  <span className="font-medium">
                    {adjustmentResult.success ? '调整成功' : '调整失败'}
                  </span>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm text-gray-600">回收时间</div>
                <div className="text-lg font-semibold text-blue-600">
                  {adjustmentResult.timeRecovered} 分钟
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm text-gray-600">影响程度</div>
                <div className={`text-lg font-semibold px-2 py-1 rounded ${getImpactColor(adjustmentResult.impactScore)}`}>
                  {getImpactText(adjustmentResult.impactScore)}
                </div>
              </div>
            </div>
          </div>

          {/* 具体调整动作 */}
          {adjustmentResult.adjustments.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">具体调整动作</h3>
              <div className="space-y-3">
                {adjustmentResult.adjustments.map((adjustment, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start">
                        <span className="text-2xl mr-3">{getActionIcon(adjustment.type)}</span>
                        <div>
                          <div className="font-medium text-gray-900">
                            {getActionDescription(adjustment.type)} - {adjustment.taskTitle}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {adjustment.reason}
                          </div>
                          <div className="text-sm text-orange-600 mt-1">
                            影响：{adjustment.impact}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right text-sm text-gray-500">
                        <div>
                          {adjustment.originalTime.start.toLocaleTimeString('zh-CN', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })} - {adjustment.originalTime.end.toLocaleTimeString('zh-CN', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </div>
                        <ArrowRight className="h-4 w-4 mx-auto my-1" />
                        <div>
                          {adjustment.newTime.start.toLocaleTimeString('zh-CN', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })} - {adjustment.newTime.end.toLocaleTimeString('zh-CN', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 警告信息 */}
          {adjustmentResult.warnings.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">注意事项</h3>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                  <div>
                    {adjustmentResult.warnings.map((warning, index) => (
                      <div key={index} className="text-yellow-800 text-sm">
                        • {warning}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 建议说明 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">建议说明</h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-blue-800 text-sm">
                {adjustmentResult.impactScore <= 20 && (
                  <p>✅ 这个调整方案影响很小，建议直接应用。</p>
                )}
                {adjustmentResult.impactScore > 20 && adjustmentResult.impactScore <= 40 && (
                  <p>⚠️ 这个调整方案有一定影响，但在可接受范围内。</p>
                )}
                {adjustmentResult.impactScore > 40 && adjustmentResult.impactScore <= 60 && (
                  <p>⚠️ 这个调整方案影响较大，请仔细考虑是否接受。</p>
                )}
                {adjustmentResult.impactScore > 60 && (
                  <p>❌ 这个调整方案影响很大，建议考虑其他解决方案或手动调整。</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onRejectAdjustment}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            拒绝调整
          </button>
          <button
            onClick={onApplyAdjustment}
            disabled={!adjustmentResult.success}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            应用调整
          </button>
        </div>
      </div>
    </div>
  );
}
