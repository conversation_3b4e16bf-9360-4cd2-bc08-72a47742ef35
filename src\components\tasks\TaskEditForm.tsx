'use client';

import React, { useState, useEffect } from 'react';
import { X, Save, Calendar, Clock } from 'lucide-react';
import { Task, TaskCategory, Priority } from '@/shared';
import QuadrantSelector from '@/components/QuadrantSelector';

interface TaskEditFormProps {
  task: Task | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (taskData: Partial<Task>) => Promise<void>;
}

interface FormData {
  title: string;
  description: string;
  category: TaskCategory;
  importance: Priority;
  urgency: Priority;
  deadline: string;
  estimatedDuration: number; // 以小时为单位显示
}

export default function TaskEditForm({ task, isOpen, onClose, onSave }: TaskEditFormProps) {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    category: 'work',
    importance: 3,
    urgency: 3,
    deadline: '',
    estimatedDuration: 1
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 当任务数据变化时更新表单
  useEffect(() => {
    if (task) {
      setFormData({
        title: task.title,
        description: task.description || '',
        category: task.category,
        importance: task.importance,
        urgency: task.urgency,
        deadline: task.deadline.toISOString().slice(0, 16), // 格式化为 datetime-local
        estimatedDuration: Math.round(task.estimatedDuration / 60 * 10) / 10 // 转换为小时并保留一位小数
      });
    }
  }, [task]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'importance' || name === 'urgency' ? parseInt(value) as Priority :
              name === 'estimatedDuration' ? parseFloat(value) :
              value
    }));
    
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleQuadrantChange = (importance: Priority, urgency: Priority) => {
    setFormData(prev => ({
      ...prev,
      importance,
      urgency
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '任务标题不能为空';
    }

    if (!formData.deadline) {
      newErrors.deadline = '截止时间不能为空';
    } else {
      const deadlineDate = new Date(formData.deadline);
      const now = new Date();
      if (deadlineDate <= now) {
        newErrors.deadline = '截止时间必须晚于当前时间';
      }
    }

    if (formData.estimatedDuration <= 0) {
      newErrors.estimatedDuration = '预估时长必须大于0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const updateData: Partial<Task> = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category,
        importance: formData.importance,
        urgency: formData.urgency,
        deadline: new Date(formData.deadline),
        estimatedDuration: Math.round(formData.estimatedDuration * 60) // 转换为分钟
      };

      await onSave(updateData);
      onClose();
    } catch (error) {
      console.error('Failed to save task:', error);
      setErrors({ submit: '保存失败，请重试' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {task ? '编辑任务' : '新建任务'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 任务标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              任务标题 *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.title ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="请输入任务标题"
            />
            {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
          </div>

          {/* 任务描述 */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              任务描述
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              value={formData.description}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入任务描述（可选）"
            />
          </div>

          {/* 任务分类 */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              任务分类 *
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="work">工作</option>
              <option value="improvement">自我提升</option>
              <option value="entertainment">娱乐</option>
            </select>
          </div>

          {/* 四象限优先级选择器 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              优先级设置 *
            </label>
            <QuadrantSelector
              importance={formData.importance}
              urgency={formData.urgency}
              onChange={handleQuadrantChange}
            />
          </div>

          {/* 截止时间 */}
          <div>
            <label htmlFor="deadline" className="block text-sm font-medium text-gray-700 mb-2">
              截止时间 *
            </label>
            <div className="relative">
              <input
                type="datetime-local"
                id="deadline"
                name="deadline"
                value={formData.deadline}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.deadline ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
            {errors.deadline && <p className="mt-1 text-sm text-red-600">{errors.deadline}</p>}
          </div>

          {/* 预估时长 */}
          <div>
            <label htmlFor="estimatedDuration" className="block text-sm font-medium text-gray-700 mb-2">
              预估时长（小时）*
            </label>
            <div className="relative">
              <input
                type="number"
                id="estimatedDuration"
                name="estimatedDuration"
                min="0.1"
                step="0.1"
                value={formData.estimatedDuration}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.estimatedDuration ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="1.0"
              />
              <Clock className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
            {errors.estimatedDuration && <p className="mt-1 text-sm text-red-600">{errors.estimatedDuration}</p>}
          </div>

          {/* 提交错误 */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  保存
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
