'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { supabase } from '@/lib/supabase';
import { Clock, Calendar, Coffee, Moon, Sun, ArrowRight } from 'lucide-react';

interface TimeSlot {
  start: string;
  end: string;
  type: 'work' | 'sleep' | 'meal' | 'commute' | 'personal';
  label: string;
}

export default function Onboarding() {
  const router = useRouter();
  const { user } = useAuthStore();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  
  // 用户时间配置
  const [timeConfig, setTimeConfig] = useState({
    // 工作时间
    workStart: '09:00',
    workEnd: '18:00',
    workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    
    // 睡眠时间
    sleepStart: '23:00',
    sleepEnd: '07:00',
    
    // 固定时间段
    fixedSlots: [
      { start: '07:00', end: '08:00', type: 'personal', label: '晨间例行' },
      { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' },
      { start: '18:30', end: '19:30', type: 'meal', label: '晚餐时间' },
    ] as TimeSlot[],
    
    // 通勤时间
    commuteToWork: 30, // 分钟
    commuteFromWork: 30,
    
    // 个人偏好
    preferredWorkHours: 'morning', // morning, afternoon, evening
    maxContinuousWork: 120, // 最大连续工作时间（分钟）
    breakInterval: 25, // 休息间隔（分钟）
    
    // 分类时间偏好
    categoryPreferences: {
      work: { preferredTimes: ['09:00-12:00', '14:00-17:00'], maxDaily: 480 },
      improvement: { preferredTimes: ['19:00-21:00', '07:00-08:00'], maxDaily: 120 },
      entertainment: { preferredTimes: ['20:00-22:00'], maxDaily: 180 }
    }
  });

  const handleTimeChange = (field: string, value: any) => {
    setTimeConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFixedSlotChange = (index: number, field: string, value: string) => {
    setTimeConfig(prev => ({
      ...prev,
      fixedSlots: prev.fixedSlots.map((slot, i) => 
        i === index ? { ...slot, [field]: value } : slot
      )
    }));
  };

  const addFixedSlot = () => {
    setTimeConfig(prev => ({
      ...prev,
      fixedSlots: [...prev.fixedSlots, {
        start: '20:00',
        end: '21:00',
        type: 'personal',
        label: '自定义时间'
      }]
    }));
  };

  const removeFixedSlot = (index: number) => {
    setTimeConfig(prev => ({
      ...prev,
      fixedSlots: prev.fixedSlots.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log('Attempting to save user configuration...'); // 尝试保存用户配置
      console.log('User ID:', user.id);
      console.log('User Email:', user.email);
      console.log('Time Config:', timeConfig);

      // 首先检查数据库连接和表结构
      console.log('Testing database connection...');
      const { data: testConnection, error: connectionError } = await supabase
        .from('user_profiles')
        .select('count', { count: 'exact', head: true });

      if (connectionError) {
        console.error('Database connection failed:', connectionError);
        throw new Error(`数据库连接失败: ${connectionError.message}`);
      }

      console.log('Database connection successful');

      // 检查用户是否已存在
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      console.log('Existing profile:', existingProfile);
      console.log('Fetch error:', fetchError);

      // 准备要保存的数据
      const profileData = {
        id: user.id,
        email: user.email!,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        work_hours: {
          start: timeConfig.workStart,
          end: timeConfig.workEnd,
          days: timeConfig.workDays
        }
      };

      // 如果表中有 time_config 和 onboarding_completed 字段，则添加它们
      try {
        const extendedData = {
          ...profileData,
          time_config: timeConfig,
          onboarding_completed: true
        };

        console.log('Attempting to save with extended data:', extendedData);

        const { data, error } = await supabase
          .from('user_profiles')
          .upsert(extendedData, { onConflict: 'id' })
          .select();

        console.log('Save result:', { data, error });

        if (error) {
          console.error('Extended save failed, trying basic save:', error);

          // 如果扩展保存失败，尝试基本保存
          const { data: basicData, error: basicError } = await supabase
            .from('user_profiles')
            .upsert(profileData, { onConflict: 'id' })
            .select();

          console.log('Basic save result:', { data: basicData, error: basicError });

          if (basicError) throw basicError;

          alert('配置已保存，但部分高级功能可能不可用。请联系管理员更新数据库结构。');
        }

      } catch (extendedError) {
        console.error('Extended save failed:', extendedError);
        throw extendedError;
      }

      router.push('/dashboard');
    } catch (error) {
      console.error('Failed to save time configuration:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // 提供更详细的错误信息
      let errorMessage = '保存配置失败，请重试';
      if (error && typeof error === 'object') {
        if ('message' in error) {
          errorMessage = `保存失败: ${error.message}`;
        } else if ('details' in error) {
          errorMessage = `保存失败: ${error.details}`;
        }
      }

      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const calculateAvailableTime = () => {
    // 计算每日可用时间
    const workMinutes = calculateTimeDiff(timeConfig.workStart, timeConfig.workEnd);
    const sleepMinutes = calculateTimeDiff(timeConfig.sleepStart, timeConfig.sleepEnd);
    const fixedMinutes = timeConfig.fixedSlots.reduce((sum, slot) => 
      sum + calculateTimeDiff(slot.start, slot.end), 0
    );
    const commuteMinutes = timeConfig.commuteToWork + timeConfig.commuteFromWork;
    
    const totalDayMinutes = 24 * 60;
    const availableMinutes = totalDayMinutes - sleepMinutes - fixedMinutes - commuteMinutes;
    const workAvailableMinutes = workMinutes;
    const personalAvailableMinutes = availableMinutes - workMinutes;
    
    return {
      total: availableMinutes,
      work: workAvailableMinutes,
      personal: personalAvailableMinutes
    };
  };

  const calculateTimeDiff = (start: string, end: string) => {
    const [startHour, startMin] = start.split(':').map(Number);
    const [endHour, endMin] = end.split(':').map(Number);
    
    let startMinutes = startHour * 60 + startMin;
    let endMinutes = endHour * 60 + endMin;
    
    // 处理跨天情况
    if (endMinutes < startMinutes) {
      endMinutes += 24 * 60;
    }
    
    return endMinutes - startMinutes;
  };

  const availableTime = calculateAvailableTime();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">欢迎使用 TimeManager</h1>
          <p className="text-gray-600">让我们了解您的时间安排，为您提供个性化的时间规划</p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep >= step 
                    ? 'bg-indigo-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 3 && (
                  <div className={`w-16 h-1 mx-2 ${
                    currentStep > step ? 'bg-indigo-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-2">
            <span className="text-sm text-gray-500">
              {currentStep === 1 && '工作时间设置'}
              {currentStep === 2 && '生活时间安排'}
              {currentStep === 3 && '个人偏好配置'}
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          {/* Step 1: 工作时间 */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="flex items-center mb-4">
                <Clock className="h-6 w-6 text-indigo-600 mr-2" />
                <h2 className="text-xl font-semibold">工作时间设置</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    工作开始时间
                  </label>
                  <input
                    type="time"
                    value={timeConfig.workStart}
                    onChange={(e) => handleTimeChange('workStart', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    工作结束时间
                  </label>
                  <input
                    type="time"
                    value={timeConfig.workEnd}
                    onChange={(e) => handleTimeChange('workEnd', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工作日
                </label>
                <div className="grid grid-cols-7 gap-2">
                  {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day, index) => {
                    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                    const isSelected = timeConfig.workDays.includes(day);
                    
                    return (
                      <button
                        key={day}
                        type="button"
                        onClick={() => {
                          const newWorkDays = isSelected
                            ? timeConfig.workDays.filter(d => d !== day)
                            : [...timeConfig.workDays, day];
                          handleTimeChange('workDays', newWorkDays);
                        }}
                        className={`p-2 text-sm rounded-md border ${
                          isSelected
                            ? 'bg-indigo-600 text-white border-indigo-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {dayNames[index]}
                      </button>
                    );
                  })}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    上班通勤时间（分钟）
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="180"
                    value={timeConfig.commuteToWork}
                    onChange={(e) => handleTimeChange('commuteToWork', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    下班通勤时间（分钟）
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="180"
                    value={timeConfig.commuteFromWork}
                    onChange={(e) => handleTimeChange('commuteFromWork', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: 生活时间 */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="flex items-center mb-4">
                <Moon className="h-6 w-6 text-indigo-600 mr-2" />
                <h2 className="text-xl font-semibold">生活时间安排</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    睡觉时间
                  </label>
                  <input
                    type="time"
                    value={timeConfig.sleepStart}
                    onChange={(e) => handleTimeChange('sleepStart', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    起床时间
                  </label>
                  <input
                    type="time"
                    value={timeConfig.sleepEnd}
                    onChange={(e) => handleTimeChange('sleepEnd', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">固定时间段</h3>
                  <button
                    type="button"
                    onClick={addFixedSlot}
                    className="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                  >
                    添加时间段
                  </button>
                </div>

                <div className="space-y-3">
                  {timeConfig.fixedSlots.map((slot, index) => (
                    <div key={index} className="grid grid-cols-12 gap-3 items-center">
                      <div className="col-span-3">
                        <input
                          type="time"
                          value={slot.start}
                          onChange={(e) => handleFixedSlotChange(index, 'start', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
                        />
                      </div>
                      <div className="col-span-3">
                        <input
                          type="time"
                          value={slot.end}
                          onChange={(e) => handleFixedSlotChange(index, 'end', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
                        />
                      </div>
                      <div className="col-span-4">
                        <input
                          type="text"
                          value={slot.label}
                          onChange={(e) => handleFixedSlotChange(index, 'label', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md"
                          placeholder="时间段名称"
                        />
                      </div>
                      <div className="col-span-2">
                        <button
                          type="button"
                          onClick={() => removeFixedSlot(index)}
                          className="w-full px-2 py-1 text-sm text-red-600 hover:text-red-800"
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: 个人偏好 */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex items-center mb-4">
                <Sun className="h-6 w-6 text-indigo-600 mr-2" />
                <h2 className="text-xl font-semibold">个人偏好配置</h2>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最佳工作时段
                </label>
                <select
                  value={timeConfig.preferredWorkHours}
                  onChange={(e) => handleTimeChange('preferredWorkHours', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="morning">上午（精力充沛）</option>
                  <option value="afternoon">下午（稳定高效）</option>
                  <option value="evening">晚上（思维活跃）</option>
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大连续工作时间（分钟）
                  </label>
                  <input
                    type="number"
                    min="30"
                    max="240"
                    step="15"
                    value={timeConfig.maxContinuousWork}
                    onChange={(e) => handleTimeChange('maxContinuousWork', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    休息间隔（分钟）
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="60"
                    step="5"
                    value={timeConfig.breakInterval}
                    onChange={(e) => handleTimeChange('breakInterval', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              {/* 时间分析预览 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3">每日时间分析</h3>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600">{Math.round(availableTime.work / 60 * 10) / 10}h</p>
                    <p className="text-sm text-gray-600">工作时间</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">{Math.round(availableTime.personal / 60 * 10) / 10}h</p>
                    <p className="text-sm text-gray-600">个人时间</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600">{Math.round(availableTime.total / 60 * 10) / 10}h</p>
                    <p className="text-sm text-gray-600">总可用时间</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <button
              type="button"
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一步
            </button>

            {currentStep < 3 ? (
              <button
                type="button"
                onClick={() => setCurrentStep(currentStep + 1)}
                className="flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700"
              >
                下一步
                <ArrowRight className="ml-2 h-4 w-4" />
              </button>
            ) : (
              <button
                type="button"
                onClick={handleSubmit}
                disabled={loading}
                className="flex items-center px-6 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Coffee className="mr-2 h-4 w-4" />
                )}
                完成设置
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
