/**
 * 任务模型
 * 定义任务的数据结构和相关类型
 */

import {
  Task,
  TaskCategory,
  Priority,
  TaskStatus,
  Quadrant
} from '@/shared';

/**
 * 数据库任务记录类型
 * 用于与数据库交互
 */
export interface TaskRecord {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  category: string;
  importance: number;
  urgency: number;
  deadline: string;
  estimated_duration: number;
  status: string;
  postpone_count: number;
  created_at: string;
  updated_at: string;
}

/**
 * 任务创建请求类型
 */
export interface CreateTaskRequest {
  userId: string;
  title: string;
  description?: string;
  category: TaskCategory;
  importance: Priority;
  urgency: Priority;
  deadline: Date;
  estimatedDuration: number;
  status?: TaskStatus;
  postponeCount?: number;
}

/**
 * 任务更新请求类型
 */
export interface UpdateTaskRequest {
  id: string;
  title?: string;
  description?: string;
  category?: TaskCategory;
  importance?: Priority;
  urgency?: Priority;
  deadline?: Date;
  estimatedDuration?: number;
  status?: TaskStatus;
  postponeCount?: number;
}

/**
 * 任务完成请求类型
 */
export interface CompleteTaskRequest {
  id: string;
  actualDuration?: number;
  satisfactionScore?: number;
}

/**
 * 任务推迟请求类型
 */
export interface PostponeTaskRequest {
  id: string;
  reason?: string;
  newDeadline?: Date;
}

/**
 * 任务过滤条件类型
 */
export interface TaskFilter {
  userId: string;
  status?: TaskStatus | TaskStatus[];
  category?: TaskCategory | TaskCategory[];
  quadrant?: Quadrant | Quadrant[];
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
}

/**
 * 任务排序选项类型
 */
export type TaskSortOption = 
  | 'deadline' 
  | 'importance' 
  | 'urgency' 
  | 'createdAt' 
  | 'estimatedDuration' 
  | 'quadrant';

/**
 * 任务排序方向类型
 */
export type SortDirection = 'asc' | 'desc';

/**
 * 任务排序配置类型
 */
export interface TaskSortConfig {
  sortBy: TaskSortOption;
  direction: SortDirection;
}

/**
 * 任务分页选项类型
 */
export interface TaskPaginationOptions {
  page: number;
  pageSize: number;
}

/**
 * 任务分页结果类型
 */
export interface TaskPaginationResult {
  tasks: Task[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 任务统计类型
 */
export interface TaskStatistics {
  total: number;
  byStatus: Record<TaskStatus, number>;
  byCategory: Record<TaskCategory, number>;
  byQuadrant: Record<Quadrant, number>;
  overdue: number;
  dueSoon: number;
  averageCompletionTime: number;
}

/**
 * 任务完成记录类型
 */
export interface TaskCompletion {
  id: string;
  taskId: string;
  userId: string;
  completedAt: Date;
  actualDuration: number;
  estimatedDuration: number;
  efficiencyRatio: number;
  satisfactionScore?: number;
}

/**
 * 将数据库记录转换为任务对象
 */
export function mapRecordToTask(record: TaskRecord): Task {
  return {
    id: record.id,
    userId: record.user_id,
    title: record.title,
    description: record.description,
    category: record.category as TaskCategory,
    importance: record.importance as Priority,
    urgency: record.urgency as Priority,
    deadline: new Date(record.deadline),
    estimatedDuration: record.estimated_duration,
    status: record.status as TaskStatus,
    postponeCount: record.postpone_count,
    createdAt: new Date(record.created_at),
    updatedAt: new Date(record.updated_at)
  };
}

/**
 * 将任务对象转换为数据库记录
 */
export function mapTaskToRecord(task: Task | CreateTaskRequest): Omit<TaskRecord, 'id' | 'created_at' | 'updated_at'> {
  return {
    user_id: task.userId,
    title: task.title,
    description: task.description || '',
    category: task.category,
    importance: task.importance,
    urgency: task.urgency,
    deadline: task.deadline.toISOString(),
    estimated_duration: task.estimatedDuration,
    status: (task as Task).status || 'pending',
    postpone_count: (task as Task).postponeCount || 0
  };
}
