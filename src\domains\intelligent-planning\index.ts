/**
 * 智能规划域统一导出
 * 提供智能规划相关的所有功能
 */

// ============================================================================
// 算法导出
// ============================================================================

export { PlanningAlgorithm } from './algorithms/PlanningAlgorithm';
export { BalanceAlgorithm } from './algorithms/BalanceAlgorithm';
export { FixAlgorithm } from './algorithms/FixAlgorithm';
export { TimeAdjustmentAlgorithm } from './algorithms/TimeAdjustmentAlgorithm';

// ============================================================================
// 协调器导出
// ============================================================================

export { AlgorithmCoordinator } from './coordinators/AlgorithmCoordinator';

// ============================================================================
// 服务导出
// ============================================================================

export { PlanningService } from './services/PlanningService';

// ============================================================================
// 便捷实例创建
// ============================================================================

import { PlanningService } from './services/PlanningService';
import { AlgorithmCoordinator } from './coordinators/AlgorithmCoordinator';
import { PlanningAlgorithm } from './algorithms/PlanningAlgorithm';
import { BalanceAlgorithm } from './algorithms/BalanceAlgorithm';
import { FixAlgorithm } from './algorithms/FixAlgorithm';
import { TimeAdjustmentAlgorithm } from './algorithms/TimeAdjustmentAlgorithm';

/**
 * 创建规划服务实例
 */
export function createPlanningService(): PlanningService {
  return new PlanningService();
}

/**
 * 创建算法协调器实例
 */
export function createAlgorithmCoordinator(): AlgorithmCoordinator {
  return new AlgorithmCoordinator();
}

/**
 * 创建单独的算法实例
 */
export function createPlanningAlgorithm(): PlanningAlgorithm {
  return new PlanningAlgorithm();
}

export function createBalanceAlgorithm(): BalanceAlgorithm {
  return new BalanceAlgorithm();
}

export function createFixAlgorithm(): FixAlgorithm {
  return new FixAlgorithm();
}

export function createTimeAdjustmentAlgorithm(): TimeAdjustmentAlgorithm {
  return new TimeAdjustmentAlgorithm();
}

// ============================================================================
// 默认实例（单例模式）
// ============================================================================

let defaultPlanningService: PlanningService | null = null;

/**
 * 获取默认的规划服务实例（单例）
 */
export function getDefaultPlanningService(): PlanningService {
  if (!defaultPlanningService) {
    defaultPlanningService = new PlanningService();
  }
  return defaultPlanningService;
}

/**
 * 重置默认实例
 */
export function resetDefaultPlanningService(): void {
  defaultPlanningService = null;
}

// ============================================================================
// 类型重新导出（从 shared 模块）
// ============================================================================

export type {
  Task,
  DailySchedule,
  TimeSlot,
  ScoredTask,
  BalanceAnalysis,
  PostponedTaskAlert,
  AdjustmentResult,
  UserTimeConfig,
  CategoryRatios,
  WeeklyStats,
  DailyStats
} from '@/shared';

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 验证规划服务的健康状态
 */
export async function validatePlanningServiceHealth(): Promise<{
  isHealthy: boolean;
  errors: string[];
  timestamp: Date;
}> {
  const errors: string[] = [];
  let isHealthy = true;

  try {
    const service = getDefaultPlanningService();
    const status = service.getServiceStatus();
    
    if (!status.isHealthy) {
      errors.push('规划服务状态异常');
      isHealthy = false;
    }
  } catch (error) {
    errors.push(`规划服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    isHealthy = false;
  }

  return {
    isHealthy,
    errors,
    timestamp: new Date()
  };
}

/**
 * 获取智能规划域的功能清单
 */
export function getPlanningDomainFeatures(): {
  algorithms: string[];
  services: string[];
  capabilities: string[];
} {
  return {
    algorithms: [
      'PlanningAlgorithm - 智能时间规划',
      'BalanceAlgorithm - 生活平衡分析',
      'FixAlgorithm - 推迟任务修复',
      'TimeAdjustmentAlgorithm - 动态时间调整'
    ],
    services: [
      'PlanningService - 统一规划服务接口',
      'AlgorithmCoordinator - 算法协调管理'
    ],
    capabilities: [
      '基于四象限的智能任务排序',
      '考虑任务类型的时间分配',
      '生活平衡状况分析和建议',
      '推迟任务检测和修复建议',
      '任务超时的动态时间调整',
      '综合的每日规划洞察',
      '个性化的时间管理建议'
    ]
  };
}

/**
 * 获取算法性能基准测试
 */
export async function runAlgorithmBenchmark(): Promise<{
  planningTime: number;
  balanceTime: number;
  fixTime: number;
  adjustmentTime: number;
  totalTime: number;
}> {
  const coordinator = createAlgorithmCoordinator();
  
  // 模拟测试数据
  const mockTasks: Task[] = [
    {
      id: '1',
      userId: 'test',
      title: '测试任务1',
      category: 'work',
      importance: 4,
      urgency: 3,
      deadline: new Date(),
      estimatedDuration: 60,
      status: 'pending',
      postponeCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  const startTime = Date.now();
  
  // 测试规划算法
  const planningStart = Date.now();
  await coordinator.generateDailySchedule(mockTasks);
  const planningTime = Date.now() - planningStart;

  // 测试平衡算法
  const balanceStart = Date.now();
  await coordinator.analyzeLifeBalance('test');
  const balanceTime = Date.now() - balanceStart;

  // 测试修复算法
  const fixStart = Date.now();
  coordinator.analyzePostponedTasks(mockTasks);
  const fixTime = Date.now() - fixStart;

  // 测试调整算法
  const adjustmentStart = Date.now();
  coordinator.predictAdjustmentImpact(30, [], 0);
  const adjustmentTime = Date.now() - adjustmentStart;

  const totalTime = Date.now() - startTime;

  return {
    planningTime,
    balanceTime,
    fixTime,
    adjustmentTime,
    totalTime
  };
}
