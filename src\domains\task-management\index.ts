/**
 * 任务管理域统一导出
 * 提供任务管理相关的所有功能
 */

// ============================================================================
// 模型导出
// ============================================================================

export type {
  TaskRecord,
  CreateTaskRequest,
  UpdateTaskRequest,
  CompleteTaskRequest,
  PostponeTaskRequest,
  TaskFilter,
  TaskSortOption,
  SortDirection,
  TaskSortConfig,
  TaskPaginationOptions,
  TaskPaginationResult,
  TaskStatistics,
  TaskCompletion
} from './models/Task';

export {
  mapRecordToTask,
  mapTaskToRecord
} from './models/Task';

// ============================================================================
// 仓储导出
// ============================================================================

export { TaskRepository } from './repositories/TaskRepository';

// ============================================================================
// 服务导出
// ============================================================================

export { TaskService } from './services/TaskService';
export { TaskValidationService } from './services/TaskValidationService';
export type { ValidationResult } from './services/TaskValidationService';

// ============================================================================
// 便捷实例创建
// ============================================================================

import { TaskService } from './services/TaskService';
import { TaskRepository } from './repositories/TaskRepository';
import { TaskValidationService } from './services/TaskValidationService';

/**
 * 创建任务服务实例
 */
export function createTaskService(): TaskService {
  return new TaskService();
}

/**
 * 创建任务仓储实例
 */
export function createTaskRepository(): TaskRepository {
  return new TaskRepository();
}

/**
 * 创建任务验证服务实例
 */
export function createTaskValidationService(): TaskValidationService {
  return new TaskValidationService();
}

// ============================================================================
// 默认实例（单例模式）
// ============================================================================

let defaultTaskService: TaskService | null = null;

/**
 * 获取默认的任务服务实例（单例）
 */
export function getDefaultTaskService(): TaskService {
  if (!defaultTaskService) {
    defaultTaskService = new TaskService();
  }
  return defaultTaskService;
}

/**
 * 重置默认实例
 */
export function resetDefaultTaskService(): void {
  defaultTaskService = null;
}

// ============================================================================
// 类型重新导出（从 shared 模块）
// ============================================================================

export type {
  Task,
  TaskCategory,
  Priority,
  TaskStatus,
  Quadrant
} from '@/shared';

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 验证任务服务的健康状态
 */
export async function validateTaskServiceHealth(): Promise<{
  isHealthy: boolean;
  errors: string[];
  timestamp: Date;
}> {
  const errors: string[] = [];
  let isHealthy = true;

  try {
    const service = getDefaultTaskService();
    
    // 尝试创建一个测试验证
    const testRequest = {
      userId: 'test',
      title: 'Test Task',
      category: 'work' as TaskCategory,
      importance: 3 as Priority,
      urgency: 3 as Priority,
      deadline: new Date(),
      estimatedDuration: 60
    };
    
    const validation = service['validationService'].validateCreateRequest(testRequest);
    if (!validation) {
      errors.push('任务验证服务不可用');
      isHealthy = false;
    }
  } catch (error) {
    errors.push(`任务服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    isHealthy = false;
  }

  return {
    isHealthy,
    errors,
    timestamp: new Date()
  };
}

/**
 * 获取任务管理域的功能清单
 */
export function getTaskManagementDomainFeatures(): {
  models: string[];
  repositories: string[];
  services: string[];
  capabilities: string[];
} {
  return {
    models: [
      'Task - 核心任务模型',
      'TaskRecord - 数据库记录映射',
      'CreateTaskRequest - 任务创建请求',
      'UpdateTaskRequest - 任务更新请求',
      'CompleteTaskRequest - 任务完成请求',
      'PostponeTaskRequest - 任务推迟请求',
      'TaskFilter - 任务过滤条件',
      'TaskStatistics - 任务统计信息'
    ],
    repositories: [
      'TaskRepository - 任务数据持久化'
    ],
    services: [
      'TaskService - 核心任务业务逻辑',
      'TaskValidationService - 任务数据验证'
    ],
    capabilities: [
      '完整的任务 CRUD 操作',
      '任务状态生命周期管理',
      '任务数据验证和业务规则检查',
      '任务过滤、排序和分页',
      '任务统计和分析',
      '批量任务操作',
      '任务完成和推迟处理',
      '过期和即将到期任务检测',
      '任务状态转换验证',
      '业务规则和警告提示'
    ]
  };
}

/**
 * 获取任务管理的性能基准测试
 */
export async function runTaskManagementBenchmark(): Promise<{
  createTime: number;
  readTime: number;
  updateTime: number;
  deleteTime: number;
  validationTime: number;
  totalTime: number;
}> {
  const service = createTaskService();
  const validationService = createTaskValidationService();
  
  // 模拟测试数据
  const testRequest: CreateTaskRequest = {
    userId: 'benchmark-test',
    title: '性能测试任务',
    category: 'work',
    importance: 3,
    urgency: 3,
    deadline: new Date(),
    estimatedDuration: 60
  };

  const startTime = Date.now();
  
  // 测试验证性能
  const validationStart = Date.now();
  validationService.validateCreateRequest(testRequest);
  const validationTime = Date.now() - validationStart;

  // 注意：这里只测试验证逻辑，不执行实际的数据库操作
  // 实际的 CRUD 操作需要数据库连接
  
  const createTime = 0; // 模拟值
  const readTime = 0;   // 模拟值
  const updateTime = 0; // 模拟值
  const deleteTime = 0; // 模拟值

  const totalTime = Date.now() - startTime;

  return {
    createTime,
    readTime,
    updateTime,
    deleteTime,
    validationTime,
    totalTime
  };
}

/**
 * 获取任务管理域的配置选项
 */
export function getTaskManagementConfig(): {
  maxTitleLength: number;
  maxDescriptionLength: number;
  maxPostponeCount: number;
  defaultPageSize: number;
  maxPageSize: number;
} {
  return {
    maxTitleLength: 100,
    maxDescriptionLength: 500,
    maxPostponeCount: 5,
    defaultPageSize: 20,
    maxPageSize: 100
  };
}

/**
 * 创建任务管理域的示例数据
 */
export function createSampleTaskData(): CreateTaskRequest[] {
  return [
    {
      userId: 'sample-user',
      title: '完成项目报告',
      description: '撰写季度项目总结报告',
      category: 'work',
      importance: 4,
      urgency: 3,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
      estimatedDuration: 120
    },
    {
      userId: 'sample-user',
      title: '学习新技术',
      description: '学习 React 18 的新特性',
      category: 'improvement',
      importance: 3,
      urgency: 2,
      deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后
      estimatedDuration: 180
    },
    {
      userId: 'sample-user',
      title: '看电影放松',
      description: '观看最新上映的电影',
      category: 'entertainment',
      importance: 2,
      urgency: 1,
      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后
      estimatedDuration: 120
    }
  ];
}
